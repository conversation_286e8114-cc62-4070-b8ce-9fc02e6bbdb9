<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>扫码入库 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
            .module-card {
                transition: all 0.3s ease;
            }
            .module-card:active {
                transform: scale(0.98);
                opacity: 0.9;
            }
            .scan-box {
                border: 2px dashed #F59E0B;
                border-radius: 12px;
                position: relative;
                overflow: hidden;
            }
            .scan-animation {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, transparent, #F59E0B, transparent);
                animation: scan 2s linear infinite;
            }
            @keyframes scan {
                0% {
                    top: 0;
                }
                50% {
                    top: 100%;
                }
                100% {
                    top: 0;
                }
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slideIn {
            animation: slideIn 0.5s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 - 带返回按钮 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button id="backButton" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">扫码入库</h1>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-8">
        <!-- 扫码区域 -->
        <section class="mb-6">
            <div class="scan-box bg-black/5 aspect-square relative overflow-hidden mb-4">
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-3/4 h-3/4 relative">
                        <!-- 模拟摄像头预览区域 -->
                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                            <i class="fa fa-camera text-4xl text-gray-400"></i>
                        </div>
                        <!-- 四个角标记 -->
                        <div class="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-accent"></div>
                        <div class="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-accent"></div>
                        <div class="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-accent"></div>
                        <div class="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-accent"></div>
                        <!-- 扫描线动画 -->
                        <div class="scan-animation"></div>
                    </div>
                </div>
            </div>
            <p class="text-center text-gray-500 text-sm">将商品条形码对准扫描框进行扫描</p>
        </section>

        <!-- 手动输入区域 -->
        <section class="mb-6">
            <div class="ionic-card">
                <h3 class="text-base font-semibold mb-3">手动输入条码</h3>
                <div class="flex items-center">
                    <input type="text" id="manualBarcode" class="ionic-input flex-1" placeholder="请输入商品条码">
                    <button id="manualScanButton" class="ml-3 px-4 py-2 bg-accent text-white rounded-lg">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- 商品信息展示区域 -->
        <section id="productInfoSection" class="mb-6 hidden">
            <div class="ionic-card animate-fadeIn">
                <h3 class="text-base font-semibold mb-4">商品信息</h3>
                <div class="flex">
                    <img id="productImage" src="https://picsum.photos/id/26/200/200" alt="商品图片" class="h-24 w-24 rounded-lg object-cover">
                    <div class="ml-4 flex-1">
                        <h4 id="productName" class="font-medium text-lg">智能手表 Pro</h4>
                        <p id="productCategory" class="text-gray-500 text-sm mt-1">电子产品</p>
                        <p id="productPrice" class="text-accent font-medium mt-2">¥1,299.00</p>
                        <p id="currentStock" class="text-gray-500 text-sm mt-1">当前库存：12件</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 入库数量输入区域 -->
        <section id="quantitySection" class="mb-6 hidden">
            <div class="ionic-card animate-fadeIn">
                <h3 class="text-base font-semibold mb-3">入库数量</h3>
                <div class="flex items-center justify-between">
                    <button id="decreaseQuantity" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center">
                        <i class="fa fa-minus text-gray-500"></i>
                    </button>
                    <input type="number" id="quantityInput" class="text-center w-16 text-xl font-medium" value="1" min="1">
                    <button id="increaseQuantity" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center">
                        <i class="fa fa-plus text-gray-500"></i>
                    </button>
                </div>
                <div class="mt-4">
                    <label for="batchNumber" class="block text-sm font-medium text-gray-700 mb-1">批次号</label>
                    <input type="text" id="batchNumber" class="ionic-input" placeholder="请输入批次号（选填）">
                </div>
                <div class="mt-4">
                    <label for="supplier" class="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                    <input type="text" id="supplier" class="ionic-input" placeholder="请输入供应商（选填）">
                </div>
            </div>
        </section>

        <!-- 操作按钮区域 -->
        <section id="actionButtons" class="mb-8 hidden">
            <button id="confirmButton" class="w-full ionic-button bg-accent text-white py-6 mb-3">
                确认入库
            </button>
            <button id="cancelButton" class="w-full ionic-button bg-white text-gray-700 border border-gray-300 py-6">
                取消
            </button>
        </section>

        <!-- 历史记录区域 -->
        <section>
            <h3 class="text-base font-semibold mb-3">最近入库记录</h3>
            <div class="space-y-3">
                <div class="ionic-card">
                    <div class="flex items-start justify-between">
                        <div>
                            <div class="flex items-center">
                                <h4 class="font-medium">智能手表 Pro</h4>
                                <span class="ml-2 px-2 py-0.5 bg-secondary/10 text-secondary rounded-full text-xs">10件</span>
                            </div>
                            <p class="text-gray-500 text-xs mt-1">批次号：B20231015001</p>
                        </div>
                        <p class="text-gray-500 text-xs">今天 14:30</p>
                    </div>
                </div>
                <div class="ionic-card">
                    <div class="flex items-start justify-between">
                        <div>
                            <div class="flex items-center">
                                <h4 class="font-medium">无线蓝牙耳机</h4>
                                <span class="ml-2 px-2 py-0.5 bg-secondary/10 text-secondary rounded-full text-xs">20件</span>
                            </div>
                            <p class="text-gray-500 text-xs mt-1">批次号：B20231015002</p>
                        </div>
                        <p class="text-gray-500 text-xs">今天 11:15</p>
                    </div>
                </div>
                <div class="ionic-card">
                    <div class="flex items-start justify-between">
                        <div>
                            <div class="flex items-center">
                                <h4 class="font-medium">礼盒装巧克力</h4>
                                <span class="ml-2 px-2 py-0.5 bg-secondary/10 text-secondary rounded-full text-xs">15件</span>
                            </div>
                            <p class="text-gray-500 text-xs mt-1">批次号：B20231014003</p>
                        </div>
                        <p class="text-gray-500 text-xs">昨天 16:45</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 成功提示弹窗 -->
    <div id="successModal" class="fixed inset-0 flex items-center justify-center bg-black/50 z-50 hidden">
        <div class="bg-white rounded-2xl p-6 w-4/5 max-w-sm text-center">
            <div class="w-16 h-16 rounded-full bg-secondary/10 flex items-center justify-center mx-auto mb-4">
                <i class="fa fa-check text-2xl text-secondary"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">入库成功</h3>
            <p class="text-gray-500 mb-6">商品已成功入库，库存已更新</p>
            <button id="closeModal" class="w-full ionic-button bg-primary text-white py-4">
                完成
            </button>
        </div>
    </div>

    <script>
        // 返回按钮事件监听
        document.getElementById('backButton').addEventListener('click', function () {
            // 返回模块索引页
            window.location.href = 'erp3.html';
        });

        // 数量增减按钮事件
        document.getElementById('decreaseQuantity').addEventListener('click', function () {
            const input = document.getElementById('quantityInput');
            if (input.value > 1) {
                input.value = parseInt(input.value) - 1;
            }
        });

        document.getElementById('increaseQuantity').addEventListener('click', function () {
            const input = document.getElementById('quantityInput');
            input.value = parseInt(input.value) + 1;
        });

        // 手动扫描按钮事件
        document.getElementById('manualScanButton').addEventListener('click', function () {
            const barcode = document.getElementById('manualBarcode').value.trim();
            if (barcode) {
                simulateScan();
            }
        });

        // 确认入库按钮事件
        document.getElementById('confirmButton').addEventListener('click', function () {
            document.getElementById('successModal').classList.remove('hidden');
        });

        // 关闭弹窗按钮事件
        document.getElementById('closeModal').addEventListener('click', function () {
            document.getElementById('successModal').classList.add('hidden');
            resetForm();
        });

        // 取消按钮事件
        document.getElementById('cancelButton').addEventListener('click', function () {
            resetForm();
        });

        // 模拟扫码功能
        function simulateScan() {
            // 显示商品信息、数量输入和操作按钮
            document.getElementById('productInfoSection').classList.remove('hidden');
            document.getElementById('quantitySection').classList.remove('hidden');
            document.getElementById('actionButtons').classList.remove('hidden');
            // 滚动到商品信息区域
            document.getElementById('productInfoSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 重置表单
        function resetForm() {
            document.getElementById('manualBarcode').value = '';
            document.getElementById('quantityInput').value = '1';
            document.getElementById('batchNumber').value = '';
            document.getElementById('supplier').value = '';
            document.getElementById('productInfoSection').classList.add('hidden');
            document.getElementById('quantitySection').classList.add('hidden');
            document.getElementById('actionButtons').classList.add('hidden');
            // 滚动到页面顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>

</html>