<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>订单管理 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1E293B',
                        light: '#F1F5F9',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        .ios-status-bar {
            height: 44px;
            background-color: #4F46E5;
        }

        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .order-card {
            transition: all 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .status-pending {
            background-color: rgba(245, 158, 11, 0.1);
            color: #F59E0B;
        }

        .status-processing {
            background-color: rgba(79, 70, 229, 0.1);
            color: #4F46E5;
        }

        .status-shipped {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
        }

        .status-completed {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
        }

        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.1);
            color: #EF4444;
        }

        .priority-high {
            color: #EF4444;
        }

        .priority-medium {
            color: #F59E0B;
        }

        .priority-low {
            color: #6B7280;
        }

        .hidden {
            display: none;
        }

        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filter-active {
            background-color: #4F46E5 !important;
            color: white !important;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">订单管理</h1>
            <div class="ml-auto flex items-center space-x-2">
                <button onclick="showNotifications()" class="p-2 relative">
                    <i class="fa fa-bell text-lg"></i>
                    <span class="absolute top-1 right-1 bg-danger text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">5</span>
                </button>
                <button onclick="showCreateOrderModal()" class="p-2">
                    <i class="fa fa-plus text-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-8">
        <!-- 快捷统计 -->
        <section class="grid grid-cols-4 gap-3 mb-6">
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-primary">2,845</div>
                <div class="text-xs text-gray-500 mt-1">总订单</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-accent">156</div>
                <div class="text-xs text-gray-500 mt-1">待处理</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-secondary">89</div>
                <div class="text-xs text-gray-500 mt-1">处理中</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-danger">12</div>
                <div class="text-xs text-gray-500 mt-1">异常</div>
            </div>
        </section>

        <!-- 搜索和筛选 -->
        <section class="mb-4">
            <div class="flex items-center mb-3">
                <div class="relative flex-1">
                    <input type="text" placeholder="搜索订单号、客户..." class="ionic-input pl-10">
                    <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button onclick="showFilterModal()" class="ml-3 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fa fa-filter text-gray-600"></i>
                </button>
            </div>

            <!-- 订单状态筛选 -->
            <div class="flex overflow-x-auto space-x-2 pb-2 scrollbar-hide">
                <button class="px-3 py-1.5 bg-primary text-white rounded-full text-sm whitespace-nowrap filter-active">全部</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterOrders('pending')">待付款</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterOrders('processing')">处理中</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterOrders('shipped')">已发货</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterOrders('completed')">已完成</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterOrders('cancelled')">已取消</button>
            </div>
        </section>

        <!-- 订单列表 -->
        <section class="space-y-3">
            <!-- 订单1 - 待付款 -->
            <div class="ionic-card order-card">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-medium text-sm">ORD-2023-1015-001</h4>
                            <span class="status-badge status-pending">待付款</span>
                            <i class="fa fa-exclamation-triangle text-xs priority-high"></i>
                        </div>
                        <p class="text-gray-500 text-xs">客户：张三 • 13812345678</p>
                        <p class="text-gray-400 text-xs mt-1">2023-10-15 10:30 • 3件商品</p>
                    </div>
                    <button onclick="showOrderMenu(1)" class="text-gray-400 p-1">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">
                            智能手表 Pro × 1, 无线耳机 × 2
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-primary">¥2,580.00</div>
                            <div class="text-xs text-gray-500">含运费 ¥15.00</div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mt-3">
                        <button onclick="processOrder(1)" class="flex-1 ionic-button bg-primary text-white text-xs py-2">
                            处理订单
                        </button>
                        <button onclick="contactCustomer(1)" class="flex-1 ionic-button bg-secondary text-white text-xs py-2">
                            联系客户
                        </button>
                    </div>
                </div>
            </div>

            <!-- 订单2 - 处理中 -->
            <div class="ionic-card order-card">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-medium text-sm">ORD-2023-1015-002</h4>
                            <span class="status-badge status-processing">处理中</span>
                            <i class="fa fa-clock-o text-xs priority-medium"></i>
                        </div>
                        <p class="text-gray-500 text-xs">客户：李四 • 13987654321</p>
                        <p class="text-gray-400 text-xs mt-1">2023-10-15 09:15 • 2件商品</p>
                    </div>
                    <button onclick="showOrderMenu(2)" class="text-gray-400 p-1">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">
                            运动鞋 × 1, 巧克力礼盒 × 1
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-primary">¥1,399.00</div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mt-3">
                        <button onclick="shipOrder(2)" class="flex-1 ionic-button bg-secondary text-white text-xs py-2">
                            安排发货
                        </button>
                        <button onclick="viewOrderDetails(2)" class="flex-1 ionic-button bg-gray-100 text-gray-600 text-xs py-2">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>

            <!-- 订单3 - 已发货 -->
            <div class="ionic-card order-card">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-medium text-sm">ORD-2023-1014-567</h4>
                            <span class="status-badge status-shipped">已发货</span>
                        </div>
                        <p class="text-gray-500 text-xs">客户：王五 • 13765432109</p>
                        <p class="text-gray-400 text-xs mt-1">2023-10-14 16:45 • 5件商品</p>
                    </div>
                    <button onclick="showOrderMenu(3)" class="text-gray-400 p-1">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">
                            快递单号: SF1234567890
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-primary">¥8,999.00</div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mt-3">
                        <button onclick="trackShipment(3)" class="flex-1 ionic-button bg-accent text-white text-xs py-2">
                            物流跟踪
                        </button>
                        <button onclick="viewOrderDetails(3)" class="flex-1 ionic-button bg-gray-100 text-gray-600 text-xs py-2">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>

            <!-- 订单4 - 已完成 -->
            <div class="ionic-card order-card">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-medium text-sm">ORD-2023-1014-566</h4>
                            <span class="status-badge status-completed">已完成</span>
                        </div>
                        <p class="text-gray-500 text-xs">客户：赵六 • 13654321098</p>
                        <p class="text-gray-400 text-xs mt-1">2023-10-14 14:20 • 1件商品</p>
                    </div>
                    <button onclick="showOrderMenu(4)" class="text-gray-400 p-1">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">
                            已确认收货 • 已评价
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-primary">¥1,250.00</div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mt-3">
                        <button onclick="viewOrderDetails(4)" class="flex-1 ionic-button bg-gray-100 text-gray-600 text-xs py-2">
                            查看详情
                        </button>
                        <button onclick="createReturnOrder(4)" class="flex-1 ionic-button bg-gray-100 text-gray-600 text-xs py-2">
                            售后处理
                        </button>
                    </div>
                </div>
            </div>

            <!-- 订单5 - 已取消 -->
            <div class="ionic-card order-card opacity-75">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-medium text-sm">ORD-2023-1014-565</h4>
                            <span class="status-badge status-cancelled">已取消</span>
                        </div>
                        <p class="text-gray-500 text-xs">客户：孙七 • 13543210987</p>
                        <p class="text-gray-400 text-xs mt-1">2023-10-14 12:10 • 取消原因：客户要求</p>
                    </div>
                    <button onclick="showOrderMenu(5)" class="text-gray-400 p-1">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">
                            已退款至原付款方式
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-gray-400">¥599.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 浮动操作按钮 -->
        <div class="fixed bottom-6 right-6">
            <button onclick="showCreateOrderModal()" class="w-14 h-14 rounded-full bg-primary text-white shadow-lg flex items-center justify-center">
                <i class="fa fa-plus text-xl"></i>
            </button>
        </div>
    </main>

    <!-- 创建订单模态框 -->
    <div id="createOrderModal" class="fixed inset-0 z-50 modal-backdrop items-center justify-center hidden">
        <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-md max-h-[90vh] overflow-y-auto fade-in">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">创建新订单</h3>
                <button onclick="closeModal('createOrderModal')" class="text-gray-400">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">客户信息</label>
                    <input type="text" class="ionic-input" placeholder="客户姓名">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                    <input type="tel" class="ionic-input" placeholder="手机号码">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">收货地址</label>
                    <textarea class="ionic-input h-20" placeholder="详细收货地址"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">商品清单</label>
                    <div class="border border-gray-200 rounded-lg p-3">
                        <button class="w-full text-left text-sm text-gray-500 flex items-center">
                            <i class="fa fa-plus mr-2"></i>
                            添加商品
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">订单金额</label>
                        <input type="number" class="ionic-input" placeholder="0.00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">运费</label>
                        <input type="number" class="ionic-input" placeholder="0.00">
                    </div>
                </div>
            </div>
            <div class="flex space-x-3 mt-6">
                <button onclick="closeModal('createOrderModal')" class="flex-1 ionic-button bg-gray-100 text-gray-600">取消</button>
                <button onclick="createOrder()" class="flex-1 ionic-button bg-primary text-white">创建订单</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function showCreateOrderModal() {
            document.getElementById('createOrderModal').classList.remove('hidden');
            document.getElementById('createOrderModal').classList.add('flex');
        }

        function showNotifications() {
            alert('显示订单通知');
        }

        function showFilterModal() {
            alert('显示筛选选项');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).classList.remove('flex');
        }

        function createOrder() {
            alert('订单创建成功！');
            closeModal('createOrderModal');
        }

        function showOrderMenu(orderId) {
            alert('显示订单 ' + orderId + ' 的操作菜单');
        }

        function processOrder(orderId) {
            alert('处理订单 ' + orderId);
        }

        function contactCustomer(orderId) {
            alert('联系订单 ' + orderId + ' 的客户');
        }

        function shipOrder(orderId) {
            alert('安排订单 ' + orderId + ' 发货');
        }

        function viewOrderDetails(orderId) {
            alert('查看订单 ' + orderId + ' 详情');
        }

        function trackShipment(orderId) {
            alert('跟踪订单 ' + orderId + ' 物流信息');
        }

        function createReturnOrder(orderId) {
            alert('处理订单 ' + orderId + ' 的售后');
        }

        function filterOrders(status) {
            document.querySelectorAll('.filter-active').forEach(btn => {
                btn.classList.remove('filter-active');
                btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-600');
            });
            
            event.target.classList.add('filter-active');
            event.target.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-600');
            
            alert('筛选状态: ' + status);
        }

        function goBack() {
            window.history.back();
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-backdrop')) {
                e.target.classList.add('hidden');
                e.target.classList.remove('flex');
            }
        });
    </script>
</body>

</html>