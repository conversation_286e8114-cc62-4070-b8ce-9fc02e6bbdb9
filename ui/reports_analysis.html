<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>报表分析 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .tab-active {
                color: #4F46E5;
                font-weight: 600;
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slideIn {
            animation: slideIn 0.5s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-button:active {
            opacity: 0.8;
            transform: scale(0.98);
        }

        /* 页面容器 */
        .page-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <div class="page-container">
        <!-- iOS状态栏占位 -->
        <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

        <!-- 顶部导航栏 -->
        <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
            <div class="flex items-center">
                <button id="backButton" class="p-2 -ml-2">
                    <i class="fa fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-xl font-bold ml-2">报表分析</h1>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="container mx-auto px-4 pt-28 pb-8 flex-1">
            <!-- 报表类型选择 -->
            <div class="flex overflow-x-auto space-x-2 mb-6 pb-2 scrollbar-hide">
                <button class="px-4 py-2 bg-primary text-white rounded-full text-sm whitespace-nowrap">销售报表</button>
                <button class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">采购报表</button>
                <button class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">库存报表</button>
                <button class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">客户报表</button>
            </div>

            <!-- 时间筛选 -->
            <div class="ionic-card mb-6">
                <div class="flex justify-between items-center">
                    <button class="px-3 py-1.5 bg-primary/10 text-primary rounded-lg text-sm">本周</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本月</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本季度</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本年</button>
                </div>
            </div>

            <!-- 销售趋势图表 -->
            <section class="ionic-card mb-6 animate-fadeIn">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-semibold">销售趋势</h3>
                </div>
                <div class="h-64">
                    <canvas id="salesChart"></canvas>
                </div>
            </section>

            <!-- 销售分类统计 -->
            <section class="ionic-card mb-6 animate-fadeIn" style="animation-delay: 0.2s;">
                <h3 class="text-base font-semibold mb-4">销售分类</h3>
                <div class="h-64 mb-4">
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-primary"></span>
                            <span class="text-sm text-gray-700">电子产品</span>
                        </div>
                        <span class="font-medium">45%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-secondary"></span>
                            <span class="text-sm text-gray-700">服装鞋帽</span>
                        </div>
                        <span class="font-medium">25%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-accent"></span>
                            <span class="text-sm text-gray-700">食品饮料</span>
                        </div>
                        <span class="font-medium">15%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-danger"></span>
                            <span class="text-sm text-gray-700">其他</span>
                        </div>
                        <span class="font-medium">15%</span>
                    </div>
                </div>
            </section>

            <!-- 数据指标卡片 -->
            <section class="space-y-4 mb-6">
                <!-- 总销售额卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn" style="animation-delay: 0.3s;">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">总销售额</p>
                            <h3 class="text-2xl font-bold mt-1">¥128,560</h3>
                        </div>
                        <div class="p-3 bg-primary/10 text-primary rounded-lg">
                            <i class="fa fa-line-chart text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-secondary mt-2">
                        <i class="fa fa-arrow-up"></i>
                        <span class="font-medium">12.5%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>

                <!-- 订单数量卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn" style="animation-delay: 0.4s;">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">订单数量</p>
                            <h3 class="text-2xl font-bold mt-1">2,845</h3>
                        </div>
                        <div class="p-3 bg-secondary/10 text-secondary rounded-lg">
                            <i class="fa fa-shopping-cart text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-secondary mt-2">
                        <i class="fa fa-arrow-up"></i>
                        <span class="font-medium">8.3%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>

                <!-- 客单价卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn" style="animation-delay: 0.5s;">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">平均客单价</p>
                            <h3 class="text-2xl font-bold mt-1">¥452.8</h3>
                        </div>
                        <div class="p-3 bg-accent/10 text-accent rounded-lg">
                            <i class="fa fa-user text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-secondary mt-2">
                        <i class="fa fa-arrow-up"></i>
                        <span class="font-medium">5.7%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>
            </section>

            <!-- 热销商品排行 -->
            <section class="ionic-card mb-6 animate-fadeIn" style="animation-delay: 0.6s;">
                <h3 class="text-base font-semibold mb-4">热销商品排行</h3>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold mr-3">1</div>
                        <img src="https://picsum.photos/id/26/200/200" alt="智能手表" class="h-14 w-14 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">智能手表 Pro</h4>
                            <p class="text-gray-500 text-xs">电子产品</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">¥128,560</p>
                            <p class="text-gray-500 text-xs">356件</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-secondary text-white rounded-full flex items-center justify-center font-bold mr-3">2</div>
                        <img src="https://picsum.photos/id/96/200/200" alt="无线耳机" class="h-14 w-14 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">无线蓝牙耳机</h4>
                            <p class="text-gray-500 text-xs">电子产品</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">¥98,230</p>
                            <p class="text-gray-500 text-xs">245件</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-accent text-white rounded-full flex items-center justify-center font-bold mr-3">3</div>
                        <img src="https://picsum.photos/id/3/200/200" alt="休闲运动鞋" class="h-14 w-14 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">轻便休闲运动鞋</h4>
                            <p class="text-gray-500 text-xs">服装鞋帽</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">¥85,760</p>
                            <p class="text-gray-500 text-xs">321件</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 导出按钮 -->
            <button class="w-full ionic-button bg-primary text-white mb-6 animate-fadeIn" style="animation-delay: 0.7s;">
                <i class="fa fa-download mr-2"></i>
                导出报表
            </button>
        </main>
    </div>

    <!-- 脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化销售趋势图表
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            const salesChart = new Chart(salesCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额',
                        data: [125000, 156000, 132000, 189000, 165000, 214000],
                        backgroundColor: '#4F46E5',
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            displayColors: false,
                            callbacks: {
                                label: function (context) {
                                    return '销售额: ¥' + context.raw.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            grid: {
                                color: '#E2E8F0'
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                },
                                callback: function (value) {
                                    return '¥' + (value / 1000) + 'k';
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });

            // 初始化销售分类图表
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            const categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['电子产品', '服装鞋帽', '食品饮料', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 15],
                        backgroundColor: [
                            '#4F46E5',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            callbacks: {
                                label: function (context) {
                                    return context.label + ': ' + context.raw + '%';
                                }
                            }
                        }
                    },
                    animations: {
                        animateRotate: true,
                        animateScale: true
                    }
                }
            });

            // 报表类型切换功能
            const reportTypeButtons = document.querySelectorAll('.scrollbar-hide button');
            reportTypeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    reportTypeButtons.forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('bg-white', 'border', 'border-gray-200');
                    });
                    
                    // 设置当前按钮为激活状态
                    this.classList.remove('bg-white', 'border', 'border-gray-200');
                    this.classList.add('bg-primary', 'text-white');
                });
            });

            // 时间筛选功能
            const timeFilterButtons = document.querySelectorAll('.ionic-card button');
            timeFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    timeFilterButtons.forEach(btn => {
                        btn.classList.remove('bg-primary/10', 'text-primary');
                        btn.classList.add('bg-white', 'border', 'border-gray-200');
                    });
                    
                    // 设置当前按钮为激活状态
                    this.classList.remove('bg-white', 'border', 'border-gray-200');
                    this.classList.add('bg-primary/10', 'text-primary');
                });
            });

            // 返回按钮功能
            document.getElementById('backButton').addEventListener('click', function() {
                // 在实际应用中可能需要导航回上一页
                window.history.back();
            });

            // 导出按钮功能
            const exportButton = document.querySelector('.ionic-button.bg-primary');
            exportButton.addEventListener('click', function() {
                alert('报表导出中，请稍候...');
            });
        });
    </script>
</body>

</html>