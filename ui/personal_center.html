<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人中心 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .text-balance {
                text-wrap: balance;
            }
            .tab-active {
                color: #4F46E5;
                font-weight: 600;
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
            .menu-item-active {
                background-color: rgba(79, 70, 229, 0.05);
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slideIn {
            animation: slideIn 0.5s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-button:active {
            opacity: 0.8;
            transform: scale(0.98);
        }

        /* 菜单项交互效果 */
        .menu-item {
            transition: all 0.2s ease;
        }

        .menu-item:active {
            background-color: rgba(79, 70, 229, 0.1);
        }

        /* 页面容器 */
        .page-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <div class="page-container">
        <!-- iOS状态栏占位 -->
        <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

        <!-- 顶部导航栏 -->
        <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
            <div class="flex items-center">
                <button id="backButton" class="p-2 -ml-2">
                    <i class="fa fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-xl font-bold ml-2">个人中心</h1>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="container mx-auto px-4 pt-28 pb-8 flex-1">
            <!-- 用户信息卡片 -->
            <div class="flex flex-col items-center p-6 bg-white rounded-xl mb-6 animate-fadeIn">
                <div class="relative">
                    <img src="https://picsum.photos/id/64/200/200" alt="用户头像" 
                        class="h-24 w-24 rounded-full object-cover border-4 border-primary">
                    <div class="absolute bottom-0 right-0 bg-primary text-white w-8 h-8 rounded-full flex items-center justify-center shadow-lg">
                        <i class="fa fa-camera"></i>
                    </div>
                </div>
                <h3 class="text-xl font-bold mt-4">王老板</h3>
                <p class="text-gray-500 mt-1">高级管理员</p>
                <div class="flex items-center mt-3 space-x-4">
                    <div class="text-center">
                        <p class="text-lg font-bold">128</p>
                        <p class="text-xs text-gray-500">今日订单</p>
                    </div>
                    <div class="w-px h-8 bg-gray-200"></div>
                    <div class="text-center">
                        <p class="text-lg font-bold">¥28,560</p>
                        <p class="text-xs text-gray-500">今日营收</p>
                    </div>
                    <div class="w-px h-8 bg-gray-200"></div>
                    <div class="text-center">
                        <p class="text-lg font-bold">98%</p>
                        <p class="text-xs text-gray-500">完成率</p>
                    </div>
                </div>
            </div>

            <!-- 功能菜单组1 -->
            <section class="mb-6 animate-fadeIn" style="animation-delay: 0.2s;">
                <h3 class="text-sm font-semibold text-gray-500 mb-3 px-2">账户设置</h3>
                <div class="space-y-1">
                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                <i class="fa fa-user"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">个人资料</h4>
                                <p class="text-gray-500 text-xs">查看和编辑个人信息</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                <i class="fa fa-lock"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">修改密码</h4>
                                <p class="text-gray-500 text-xs">定期更换密码保障安全</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                                <i class="fa fa-bell"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">消息通知</h4>
                                <p class="text-gray-500 text-xs">管理通知偏好设置</p>
                            </div>
                            <div class="flex items-center">
                                <span class="px-2 py-1 bg-danger text-white text-xs rounded-full mr-2">3</span>
                                <i class="fa fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 功能菜单组2 -->
            <section class="mb-6 animate-fadeIn" style="animation-delay: 0.4s;">
                <h3 class="text-sm font-semibold text-gray-500 mb-3 px-2">系统功能</h3>
                <div class="space-y-1">
                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                <i class="fa fa-cog"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">系统设置</h4>
                                <p class="text-gray-500 text-xs">应用配置和偏好设置</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                <i class="fa fa-user-circle-o"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">权限管理</h4>
                                <p class="text-gray-500 text-xs">管理用户角色和权限</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                                <i class="fa fa-history"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">操作日志</h4>
                                <p class="text-gray-500 text-xs">查看系统操作记录</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 功能菜单组3 -->
            <section class="mb-6 animate-fadeIn" style="animation-delay: 0.6s;">
                <h3 class="text-sm font-semibold text-gray-500 mb-3 px-2">帮助与支持</h3>
                <div class="space-y-1">
                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                <i class="fa fa-question-circle"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">帮助中心</h4>
                                <p class="text-gray-500 text-xs">常见问题和操作指南</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                <i class="fa fa-commenting"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">在线客服</h4>
                                <p class="text-gray-500 text-xs">获取专业技术支持</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="ionic-card p-4 menu-item">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                                <i class="fa fa-info-circle"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium">关于我们</h4>
                                <p class="text-gray-500 text-xs">版本信息：v2.5.1</p>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 退出登录 -->
            <button id="logoutButton" class="w-full ionic-button bg-white border border-danger text-danger animate-fadeIn" style="animation-delay: 0.8s;">
                <i class="fa fa-sign-out mr-2"></i>
                退出登录
            </button>
        </main>
    </div>

    <!-- 脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 返回按钮功能
            document.getElementById('backButton').addEventListener('click', function() {
                // 在实际应用中可能需要导航回上一页
                window.history.back();
            });

            // 退出登录按钮功能
            document.getElementById('logoutButton').addEventListener('click', function() {
                if (confirm('确定要退出登录吗？')) {
                    // 在实际应用中可能需要清除本地存储并跳转到登录页
                    alert('已退出登录');
                }
            });

            // 菜单项点击效果
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 模拟页面跳转或展开详情
                    const title = this.querySelector('h4').textContent;
                    alert(`进入${title}页面`);
                });
            });

            // 头像编辑功能
            const avatarContainer = document.querySelector('.relative');
            avatarContainer.addEventListener('click', function() {
                alert('选择图片方式：\n1. 拍照\n2. 从相册选择');
            });
        });
    </script>
</body>

</html>