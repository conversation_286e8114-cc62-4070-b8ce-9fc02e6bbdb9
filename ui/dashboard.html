<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家ERP系统 - 仪表盘</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>
    
    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .grid-dashboard {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
        }
    </style>
    
    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
        }
        
        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }
        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        /* 数据卡片动画延迟 */
        .card-delay-1 {
            animation-delay: 0.1s;
        }
        .card-delay-2 {
            animation-delay: 0.2s;
        }
        .card-delay-3 {
            animation-delay: 0.3s;
        }
        .card-delay-4 {
            animation-delay: 0.4s;
        }
    </style>
</head>
<body class="font-inter text-dark overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50 transition-all duration-300" id="mainHeader">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <!-- 左侧Logo和标题 -->
            <div class="flex items-center space-x-2">
                <div class="bg-primary text-white p-2 rounded-lg">
                    <i class="fa fa-cubes text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-primary hidden md:block">商家ERP系统</h1>
            </div>
            
            <!-- 中间搜索框 -->
            <div class="hidden md:flex relative flex-1 max-w-md mx-8">
                <input type="text" placeholder="搜索商品、订单或客户..." 
                    class="w-full py-2 px-4 pl-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all">
                <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            </div>
            
            <!-- 右侧用户信息和操作 -->
            <div class="flex items-center space-x-4">
                <button class="relative p-2 text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
                    <i class="fa fa-bell text-lg"></i>
                    <span class="absolute top-1 right-1 bg-danger text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
                <div class="relative group">
                    <button class="flex items-center space-x-2 hover:bg-gray-100 p-1 rounded-lg transition-colors">
                        <img src="https://picsum.photos/id/64/200/200" alt="用户头像" class="h-8 w-8 rounded-full object-cover border-2 border-primary">
                        <span class="hidden md:inline font-medium">王老板</span>
                        <i class="fa fa-angle-down text-gray-500"></i>
                    </button>
                    <!-- 下拉菜单 -->
                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                            <i class="fa fa-user-circle text-primary"></i>
                            <span>个人资料</span>
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                            <i class="fa fa-cog text-primary"></i>
                            <span>系统设置</span>
                        </a>
                        <div class="border-t border-gray-200 my-1"></div>
                        <a href="#" class="block px-4 py-2 text-sm text-danger hover:bg-gray-100 flex items-center space-x-2">
                            <i class="fa fa-sign-out"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 pt-24 pb-12">
        <!-- 欢迎信息和日期 -->
        <section class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-balance">
                        您好，王老板！
                    </h2>
                    <p class="text-gray-500 mt-1" id="currentDate">2023年10月15日 星期日</p>
                </div>
                <div class="flex space-x-3 mt-4 md:mt-0">
                    <button class="px-4 py-2 bg-primary text-white rounded-lg shadow flex items-center space-x-2 hover:bg-primary/90 transition-colors">
                        <i class="fa fa-plus"></i>
                        <span>新建订单</span>
                    </button>
                    <button class="px-4 py-2 bg-white text-dark border border-gray-300 rounded-lg shadow flex items-center space-x-2 hover:bg-gray-50 transition-colors">
                        <i class="fa fa-download text-primary"></i>
                        <span>导出报表</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- 数据概览卡片 -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-dashboard gap-6 mb-8">
            <!-- 总销售额卡片 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift animate-fadeIn card-delay-1">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <p class="text-gray-500 font-medium">总销售额</p>
                        <h3 class="text-2xl font-bold mt-1">¥128,560</h3>
                    </div>
                    <div class="p-3 bg-primary/10 text-primary rounded-lg">
                        <i class="fa fa-line-chart text-xl"></i>
                    </div>
                </div>
                <div class="flex items-center space-x-1 text-secondary">
                    <i class="fa fa-arrow-up"></i>
                    <span class="font-medium">12.5%</span>
                    <span class="text-gray-500 text-sm">较上月</span>
                </div>
            </div>

            <!-- 订单数量卡片 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift animate-fadeIn card-delay-2">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <p class="text-gray-500 font-medium">订单数量</p>
                        <h3 class="text-2xl font-bold mt-1">2,845</h3>
                    </div>
                    <div class="p-3 bg-secondary/10 text-secondary rounded-lg">
                        <i class="fa fa-shopping-cart text-xl"></i>
                    </div>
                </div>
                <div class="flex items-center space-x-1 text-secondary">
                    <i class="fa fa-arrow-up"></i>
                    <span class="font-medium">8.3%</span>
                    <span class="text-gray-500 text-sm">较上月</span>
                </div>
            </div>

            <!-- 商品库存卡片 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift animate-fadeIn card-delay-3">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <p class="text-gray-500 font-medium">商品库存</p>
                        <h3 class="text-2xl font-bold mt-1">3,456</h3>
                    </div>
                    <div class="p-3 bg-accent/10 text-accent rounded-lg">
                        <i class="fa fa-cubes text-xl"></i>
                    </div>
                </div>
                <div class="flex items-center space-x-1 text-danger">
                    <i class="fa fa-arrow-down"></i>
                    <span class="font-medium">5.2%</span>
                    <span class="text-gray-500 text-sm">较上月</span>
                </div>
            </div>

            <!-- 客户数量卡片 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift animate-fadeIn card-delay-4">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <p class="text-gray-500 font-medium">客户数量</p>
                        <h3 class="text-2xl font-bold mt-1">1,280</h3>
                    </div>
                    <div class="p-3 bg-primary/10 text-primary rounded-lg">
                        <i class="fa fa-users text-xl"></i>
                    </div>
                </div>
                <div class="flex items-center space-x-1 text-secondary">
                    <i class="fa fa-arrow-up"></i>
                    <span class="font-medium">15.8%</span>
                    <span class="text-gray-500 text-sm">较上月</span>
                </div>
            </div>
        </section>

        <!-- 图表和数据部分 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 销售趋势图表 -->
            <div class="lg:col-span-2 bg-white rounded-xl p-6 shadow-md hover-lift">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">销售趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-primary text-white rounded-full">周</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">月</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">年</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            <!-- 销售分类统计 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift">
                <h3 class="text-lg font-semibold mb-6">销售分类</h3>
                <div class="h-64 mb-4">
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-primary"></span>
                            <span class="text-sm text-gray-700">电子产品</span>
                        </div>
                        <span class="font-medium">45%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-secondary"></span>
                            <span class="text-sm text-gray-700">服装鞋帽</span>
                        </div>
                        <span class="font-medium">25%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-accent"></span>
                            <span class="text-sm text-gray-700">食品饮料</span>
                        </div>
                        <span class="font-medium">15%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-danger"></span>
                            <span class="text-sm text-gray-700">其他</span>
                        </div>
                        <span class="font-medium">15%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近订单和低库存商品 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 最近订单 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">最近订单</h3>
                    <a href="#" class="text-primary text-sm font-medium hover:underline">查看全部</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-500">订单号</th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-500">客户</th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-500">金额</th>
                                <th class="text-left py-3 px-4 text-sm font-medium text-gray-500">状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                <td class="py-3 px-4 text-sm">ORD-2023-1015-001</td>
                                <td class="py-3 px-4 text-sm">张三</td>
                                <td class="py-3 px-4 text-sm font-medium">¥2,580.00</td>
                                <td class="py-3 px-4 text-sm"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span></td>
                            </tr>
                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                <td class="py-3 px-4 text-sm">ORD-2023-1015-002</td>
                                <td class="py-3 px-4 text-sm">李四</td>
                                <td class="py-3 px-4 text-sm font-medium">¥1,399.00</td>
                                <td class="py-3 px-4 text-sm"><span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-xs">处理中</span></td>
                            </tr>
                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                <td class="py-3 px-4 text-sm">ORD-2023-1014-567</td>
                                <td class="py-3 px-4 text-sm">王五</td>
                                <td class="py-3 px-4 text-sm font-medium">¥8,999.00</td>
                                <td class="py-3 px-4 text-sm"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span></td>
                            </tr>
                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                <td class="py-3 px-4 text-sm">ORD-2023-1014-566</td>
                                <td class="py-3 px-4 text-sm">赵六</td>
                                <td class="py-3 px-4 text-sm font-medium">¥1,250.00</td>
                                <td class="py-3 px-4 text-sm"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-xs">已取消</span></td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="py-3 px-4 text-sm">ORD-2023-1014-565</td>
                                <td class="py-3 px-4 text-sm">钱七</td>
                                <td class="py-3 px-4 text-sm font-medium">¥3,499.00</td>
                                <td class="py-3 px-4 text-sm"><span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">待付款</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 低库存商品 -->
            <div class="bg-white rounded-xl p-6 shadow-md hover-lift">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">低库存商品</h3>
                    <a href="#" class="text-primary text-sm font-medium hover:underline">查看全部</a>
                </div>
                <div class="space-y-4">
                    <!-- 商品1 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <img src="https://picsum.photos/id/26/200/200" alt="智能手表" class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-4 flex-1">
                            <h4 class="font-medium">智能手表 Pro</h4>
                            <p class="text-gray-500 text-sm">电子产品</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center justify-end space-x-1 text-danger">
                                <i class="fa fa-exclamation-circle"></i>
                                <span class="font-medium">仅余12件</span>
                            </div>
                            <button class="mt-2 px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">补货</button>
                        </div>
                    </div>
                    <!-- 商品2 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <img src="https://picsum.photos/id/96/200/200" alt="无线耳机" class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-4 flex-1">
                            <h4 class="font-medium">无线蓝牙耳机</h4>
                            <p class="text-gray-500 text-sm">电子产品</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center justify-end space-x-1 text-danger">
                                <i class="fa fa-exclamation-circle"></i>
                                <span class="font-medium">仅余8件</span>
                            </div>
                            <button class="mt-2 px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">补货</button>
                        </div>
                    </div>
                    <!-- 商品3 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <img src="https://picsum.photos/id/3/200/200" alt="休闲运动鞋" class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-4 flex-1">
                            <h4 class="font-medium">轻便休闲运动鞋</h4>
                            <p class="text-gray-500 text-sm">服装鞋帽</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center justify-end space-x-1 text-danger">
                                <i class="fa fa-exclamation-circle"></i>
                                <span class="font-medium">仅余15件</span>
                            </div>
                            <button class="mt-2 px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">补货</button>
                        </div>
                    </div>
                    <!-- 商品4 -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <img src="https://picsum.photos/id/431/200/200" alt="高端巧克力" class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-4 flex-1">
                            <h4 class="font-medium">礼盒装巧克力</h4>
                            <p class="text-gray-500 text-sm">食品饮料</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center justify-end space-x-1 text-danger">
                                <i class="fa fa-exclamation-circle"></i>
                                <span class="font-medium">仅余5件</span>
                            </div>
                            <button class="mt-2 px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">补货</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="bg-white border-t border-gray-200 py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500 text-sm">© 2023 商家ERP系统. 保留所有权利.</p>
                </div>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-500 hover:text-primary transition-colors"><i class="fa fa-question-circle"></i> 帮助中心</a>
                    <a href="#" class="text-gray-500 hover:text-primary transition-colors"><i class="fa fa-file-text-o"></i> 使用条款</a>
                    <a href="#" class="text-gray-500 hover:text-primary transition-colors"><i class="fa fa-shield"></i> 隐私政策</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 脚本 -->
    <script>
        // 设置当前日期
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('zh-CN', options);
            
            // 导航栏滚动效果
            const header = document.getElementById('mainHeader');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 10) {
                    header.classList.add('py-2');
                    header.classList.add('shadow');
                } else {
                    header.classList.remove('py-2');
                    header.classList.remove('shadow');
                }
            });
            
            // 初始化销售趋势图表
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            const salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '销售额',
                        data: [12500, 15600, 13200, 18900, 16500, 21400, 20300],
                        borderColor: '#4F46E5',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#4F46E5',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            displayColors: false,
                            callbacks: {
                                label: function(context) {
                                    return '销售额: ¥' + context.raw.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#94A3B8'
                            }
                        },
                        y: {
                            grid: {
                                color: '#E2E8F0'
                            },
                            ticks: {
                                color: '#94A3B8',
                                callback: function(value) {
                                    return '¥' + (value / 1000) + 'k';
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    animations: {
                        tension: {
                            duration: 1000,
                            easing: 'linear'
                        }
                    }
                }
            });
            
            // 初始化销售分类图表
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            const categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['电子产品', '服装鞋帽', '食品饮料', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 15],
                        backgroundColor: [
                            '#4F46E5',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.raw + '%';
                                }
                            }
                        }
                    },
                    animations: {
                        animateRotate: true,
                        animateScale: true
                    }
                }
            });
        });
    </script>
</body>
</html>