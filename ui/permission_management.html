<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>权限管理 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1E293B',
                        light: '#F1F5F9',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        .ios-status-bar {
            height: 44px;
            background-color: #4F46E5;
        }

        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .permission-card {
            transition: all 0.3s ease;
        }

        .permission-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e1;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #4F46E5;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }

        .tab-active {
            background-color: #4F46E5 !important;
            color: white !important;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-admin {
            background-color: rgba(79, 70, 229, 0.1);
            color: #4F46E5;
        }

        .badge-manager {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
        }

        .badge-staff {
            background-color: rgba(245, 158, 11, 0.1);
            color: #F59E0B;
        }

        .hidden {
            display: none;
        }

        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">权限管理</h1>
            <div class="ml-auto">
                <button onclick="showAddUserModal()" class="p-2">
                    <i class="fa fa-plus text-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-8">
        <!-- 快捷统计 -->
        <section class="grid grid-cols-3 gap-4 mb-6">
            <div class="ionic-card text-center">
                <div class="text-2xl font-bold text-primary">18</div>
                <div class="text-xs text-gray-500 mt-1">总用户数</div>
            </div>
            <div class="ionic-card text-center">
                <div class="text-2xl font-bold text-secondary">5</div>
                <div class="text-xs text-gray-500 mt-1">角色数</div>
            </div>
            <div class="ionic-card text-center">
                <div class="text-2xl font-bold text-accent">2</div>
                <div class="text-xs text-gray-500 mt-1">待审核</div>
            </div>
        </section>

        <!-- 标签页 -->
        <section class="mb-6">
            <div class="flex bg-gray-100 rounded-lg p-1">
                <button id="usersTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium tab-active transition-all" onclick="switchTab('users')">
                    用户管理
                </button>
                <button id="rolesTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 transition-all" onclick="switchTab('roles')">
                    角色管理
                </button>
                <button id="permissionsTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 transition-all" onclick="switchTab('permissions')">
                    权限设置
                </button>
            </div>
        </section>

        <!-- 用户管理标签页 -->
        <div id="usersContent" class="tab-content">
            <!-- 搜索栏 -->
            <div class="flex items-center mb-4">
                <div class="relative flex-1">
                    <input type="text" placeholder="搜索用户..." class="ionic-input pl-10">
                    <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button onclick="showFilterModal()" class="ml-3 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fa fa-filter text-gray-600"></i>
                </button>
            </div>

            <!-- 用户列表 -->
            <div class="space-y-3">
                <!-- 用户1 - 管理员 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/64/200/200" alt="王老板" class="h-12 w-12 rounded-full object-cover">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-medium">王老板</h4>
                                <span class="badge badge-admin">超级管理员</span>
                            </div>
                            <p class="text-gray-500 text-xs"><EMAIL></p>
                            <p class="text-gray-400 text-xs mt-1">最后登录: 2023-10-15 14:30</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <button onclick="showUserMenu(1)" class="text-gray-400">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户2 - 店长 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/91/200/200" alt="张店长" class="h-12 w-12 rounded-full object-cover">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-medium">张店长</h4>
                                <span class="badge badge-manager">店长</span>
                            </div>
                            <p class="text-gray-500 text-xs"><EMAIL></p>
                            <p class="text-gray-400 text-xs mt-1">最后登录: 2023-10-15 11:45</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <button onclick="showUserMenu(2)" class="text-gray-400">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户3 - 销售员 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/65/200/200" alt="李销售" class="h-12 w-12 rounded-full object-cover">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-medium">李销售</h4>
                                <span class="badge badge-staff">销售员</span>
                            </div>
                            <p class="text-gray-500 text-xs"><EMAIL></p>
                            <p class="text-gray-400 text-xs mt-1">最后登录: 2023-10-14 18:20</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <button onclick="showUserMenu(3)" class="text-gray-400">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 待审核用户 -->
                <div class="ionic-card permission-card opacity-75">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/85/200/200" alt="赵新员工" class="h-12 w-12 rounded-full object-cover">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-medium">赵新员工</h4>
                                <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">待审核</span>
                            </div>
                            <p class="text-gray-500 text-xs"><EMAIL></p>
                            <p class="text-gray-400 text-xs mt-1">申请时间: 2023-10-15 09:00</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="approveUser(5)" class="px-3 py-1 bg-secondary text-white rounded-md text-xs">
                                批准
                            </button>
                            <button onclick="rejectUser(5)" class="px-3 py-1 bg-danger text-white rounded-md text-xs">
                                拒绝
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色管理标签页 -->
        <div id="rolesContent" class="tab-content hidden">
            <div class="space-y-4">
                <!-- 超级管理员角色 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                <i class="fa fa-crown"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">超级管理员</h4>
                                <p class="text-gray-500 text-xs">拥有系统所有权限</p>
                            </div>
                        </div>
                        <button onclick="editRole('admin')" class="text-primary">
                            <i class="fa fa-edit"></i>
                        </button>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">用户数量: 1人</div>
                        <div class="text-xs text-secondary">100% 权限</div>
                    </div>
                </div>

                <!-- 店长角色 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                <i class="fa fa-user-tie"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">店长</h4>
                                <p class="text-gray-500 text-xs">管理日常业务运营</p>
                            </div>
                        </div>
                        <button onclick="editRole('manager')" class="text-primary">
                            <i class="fa fa-edit"></i>
                        </button>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">用户数量: 3人</div>
                        <div class="text-xs text-secondary">85% 权限</div>
                    </div>
                </div>

                <!-- 销售员角色 -->
                <div class="ionic-card permission-card">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                                <i class="fa fa-handshake-o"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">销售员</h4>
                                <p class="text-gray-500 text-xs">处理订单和客户服务</p>
                            </div>
                        </div>
                        <button onclick="editRole('sales')" class="text-primary">
                            <i class="fa fa-edit"></i>
                        </button>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xs text-gray-500">用户数量: 8人</div>
                        <div class="text-xs text-secondary">45% 权限</div>
                    </div>
                </div>
            </div>

            <!-- 添加角色按钮 -->
            <button onclick="showAddRoleModal()" class="w-full ionic-button bg-primary text-white mt-6">
                <i class="fa fa-plus mr-2"></i>
                添加新角色
            </button>
        </div>

        <!-- 权限设置标签页 -->
        <div id="permissionsContent" class="tab-content hidden">
            <div class="space-y-4">
                <!-- 商品管理权限 -->
                <div class="ionic-card">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium flex items-center">
                            <i class="fa fa-cube text-primary mr-2"></i>
                            商品管理
                        </h4>
                        <button onclick="toggleAllPermissions('product')" class="text-xs text-primary">全选/取消</button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">查看商品</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">添加商品</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">编辑商品</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">删除商品</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 订单管理权限 -->
                <div class="ionic-card">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium flex items-center">
                            <i class="fa fa-shopping-cart text-secondary mr-2"></i>
                            订单管理
                        </h4>
                        <button onclick="toggleAllPermissions('order')" class="text-xs text-primary">全选/取消</button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">查看订单</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">创建订单</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">修改订单</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">取消订单</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 系统管理权限 -->
                <div class="ionic-card">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium flex items-center">
                            <i class="fa fa-cog text-accent mr-2"></i>
                            系统管理
                        </h4>
                        <button onclick="toggleAllPermissions('system')" class="text-xs text-primary">全选/取消</button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">用户管理</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">角色管理</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">系统设置</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 保存权限按钮 -->
            <button onclick="savePermissions()" class="w-full ionic-button bg-secondary text-white mt-6">
                <i class="fa fa-save mr-2"></i>
                保存权限设置
            </button>
        </div>
    </main>

    <!-- 添加用户模态框 -->
    <div id="addUserModal" class="fixed inset-0 z-50 modal-backdrop items-center justify-center hidden">
        <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-md fade-in">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">添加新用户</h3>
                <button onclick="closeModal('addUserModal')" class="text-gray-400">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <input type="text" class="ionic-input" placeholder="请输入姓名">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                    <input type="email" class="ionic-input" placeholder="请输入邮箱">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                    <select class="ionic-input">
                        <option>选择角色</option>
                        <option>店长</option>
                        <option>销售员</option>
                        <option>仓库管理员</option>
                        <option>财务</option>
                    </select>
                </div>
            </div>
            <div class="flex space-x-3 mt-6">
                <button onclick="closeModal('addUserModal')" class="flex-1 ionic-button bg-gray-100 text-gray-600">取消</button>
                <button onclick="addUser()" class="flex-1 ionic-button bg-primary text-white">添加</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 标签页切换
        function switchTab(tab) {
            // 移除所有标签的激活状态
            document.querySelectorAll('[id$="Tab"]').forEach(btn => {
                btn.classList.remove('tab-active');
                btn.classList.add('text-gray-600');
            });
            
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 激活当前标签
            document.getElementById(tab + 'Tab').classList.add('tab-active');
            document.getElementById(tab + 'Tab').classList.remove('text-gray-600');
            
            // 显示当前标签页内容
            document.getElementById(tab + 'Content').classList.remove('hidden');
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            document.getElementById('addUserModal').classList.remove('hidden');
            document.getElementById('addUserModal').classList.add('flex');
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).classList.remove('flex');
        }

        // 添加用户
        function addUser() {
            // 这里添加实际的添加用户逻辑
            alert('用户添加成功！');
            closeModal('addUserModal');
        }

        // 用户菜单
        function showUserMenu(userId) {
            alert('显示用户 ' + userId + ' 的操作菜单');
        }

        // 批准用户
        function approveUser(userId) {
            alert('用户 ' + userId + ' 已批准');
        }

        // 拒绝用户
        function rejectUser(userId) {
            alert('用户 ' + userId + ' 已拒绝');
        }

        // 编辑角色
        function editRole(roleId) {
            alert('编辑角色: ' + roleId);
        }

        // 显示添加角色模态框
        function showAddRoleModal() {
            alert('显示添加角色模态框');
        }

        // 切换所有权限
        function toggleAllPermissions(category) {
            alert('切换 ' + category + ' 分类的所有权限');
        }

        // 保存权限设置
        function savePermissions() {
            alert('权限设置已保存');
        }

        // 显示筛选模态框
        function showFilterModal() {
            alert('显示筛选模态框');
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-backdrop')) {
                e.target.classList.add('hidden');
                e.target.classList.remove('flex');
            }
        });
    </script>
</body>

</html>