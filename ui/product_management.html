<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>商品管理 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1E293B',
                        light: '#F1F5F9',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        .ios-status-bar {
            height: 44px;
            background-color: #4F46E5;
        }

        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .product-card {
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stock-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .stock-high {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
        }

        .stock-medium {
            background-color: rgba(245, 158, 11, 0.1);
            color: #F59E0B;
        }

        .stock-low {
            background-color: rgba(239, 68, 68, 0.1);
            color: #EF4444;
        }

        .category-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            background-color: rgba(79, 70, 229, 0.1);
            color: #4F46E5;
        }

        .hidden {
            display: none;
        }

        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filter-active {
            background-color: #4F46E5 !important;
            color: white !important;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">商品管理</h1>
            <div class="ml-auto flex items-center space-x-2">
                <button onclick="showScanModal()" class="p-2">
                    <i class="fa fa-qrcode text-lg"></i>
                </button>
                <button onclick="showAddProductModal()" class="p-2">
                    <i class="fa fa-plus text-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-8">
        <!-- 快捷统计 -->
        <section class="grid grid-cols-4 gap-3 mb-6">
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-primary">1,248</div>
                <div class="text-xs text-gray-500 mt-1">总商品</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-secondary">156</div>
                <div class="text-xs text-gray-500 mt-1">在售</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-accent">23</div>
                <div class="text-xs text-gray-500 mt-1">低库存</div>
            </div>
            <div class="ionic-card text-center py-3">
                <div class="text-xl font-bold text-danger">8</div>
                <div class="text-xs text-gray-500 mt-1">缺货</div>
            </div>
        </section>

        <!-- 搜索和筛选 -->
        <section class="mb-4">
            <div class="flex items-center mb-3">
                <div class="relative flex-1">
                    <input type="text" placeholder="搜索商品名称、编号..." class="ionic-input pl-10">
                    <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button onclick="showFilterModal()" class="ml-3 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fa fa-filter text-gray-600"></i>
                </button>
            </div>

            <!-- 快捷筛选 -->
            <div class="flex overflow-x-auto space-x-2 pb-2">
                <button class="px-3 py-1.5 bg-primary text-white rounded-full text-sm whitespace-nowrap filter-active">全部</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterProducts('electronics')">电子产品</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterProducts('clothing')">服装鞋帽</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterProducts('food')">食品饮料</button>
                <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap" onclick="filterProducts('low-stock')">低库存</button>
            </div>
        </section>

        <!-- 商品列表 -->
        <section class="space-y-3">
            <!-- 商品1 - 智能手表 -->
            <div class="ionic-card product-card">
                <div class="flex">
                    <img src="https://picsum.photos/id/26/200/200" alt="智能手表" class="h-20 w-20 rounded-lg object-cover">
                    <div class="ml-3 flex-1">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">智能运动手表 Pro</h4>
                                <p class="text-gray-500 text-xs mt-1">SKU: SW001</p>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="category-badge">电子产品</span>
                                    <span class="stock-low">库存: 12</span>
                                </div>
                            </div>
                            <button onclick="showProductMenu(1)" class="text-gray-400 p-1">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-3">
                                <span class="text-lg font-bold text-primary">¥1,299</span>
                                <span class="text-xs text-gray-400 line-through">¥1,599</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="quickEdit(1)" class="text-primary text-xs">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button onclick="toggleProductStatus(1)" class="text-secondary text-xs">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商品2 - 无线耳机 -->
            <div class="ionic-card product-card">
                <div class="flex">
                    <img src="https://picsum.photos/id/96/200/200" alt="无线耳机" class="h-20 w-20 rounded-lg object-cover">
                    <div class="ml-3 flex-1">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">无线蓝牙耳机</h4>
                                <p class="text-gray-500 text-xs mt-1">SKU: BT002</p>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="category-badge">电子产品</span>
                                    <span class="stock-low">库存: 8</span>
                                </div>
                            </div>
                            <button onclick="showProductMenu(2)" class="text-gray-400 p-1">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-3">
                                <span class="text-lg font-bold text-primary">¥899</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="quickEdit(2)" class="text-primary text-xs">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button onclick="toggleProductStatus(2)" class="text-secondary text-xs">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商品3 - 运动鞋 -->
            <div class="ionic-card product-card">
                <div class="flex">
                    <img src="https://picsum.photos/id/3/200/200" alt="运动鞋" class="h-20 w-20 rounded-lg object-cover">
                    <div class="ml-3 flex-1">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">轻便休闲运动鞋</h4>
                                <p class="text-gray-500 text-xs mt-1">SKU: SH003</p>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="category-badge">服装鞋帽</span>
                                    <span class="stock-high">库存: 85</span>
                                </div>
                            </div>
                            <button onclick="showProductMenu(3)" class="text-gray-400 p-1">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-3">
                                <span class="text-lg font-bold text-primary">¥399</span>
                                <span class="text-xs text-gray-400 line-through">¥499</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="quickEdit(3)" class="text-primary text-xs">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button onclick="toggleProductStatus(3)" class="text-secondary text-xs">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 缺货商品 -->
            <div class="ionic-card product-card opacity-75">
                <div class="flex">
                    <div class="h-20 w-20 rounded-lg bg-gray-200 flex items-center justify-center">
                        <i class="fa fa-image text-gray-400 text-2xl"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">高端咖啡豆</h4>
                                <p class="text-gray-500 text-xs mt-1">SKU: CF005</p>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="category-badge">食品饮料</span>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">缺货</span>
                                </div>
                            </div>
                            <button onclick="showProductMenu(5)" class="text-gray-400 p-1">
                                <i class="fa fa-ellipsis-v"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center space-x-3">
                                <span class="text-lg font-bold text-gray-400">¥299</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="restockProduct(5)" class="px-3 py-1 bg-secondary text-white rounded-md text-xs">
                                    补货
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 添加商品模态框 -->
    <div id="addProductModal" class="fixed inset-0 z-50 modal-backdrop items-center justify-center hidden">
        <div class="bg-white rounded-xl p-6 mx-4 w-full max-w-md max-h-[90vh] overflow-y-auto fade-in">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">添加新商品</h3>
                <button onclick="closeModal('addProductModal')" class="text-gray-400">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">商品图片</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <i class="fa fa-camera text-gray-400 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-500">点击上传图片</p>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">商品名称</label>
                    <input type="text" class="ionic-input" placeholder="请输入商品名称">
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">售价</label>
                        <input type="number" class="ionic-input" placeholder="0.00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">库存</label>
                        <input type="number" class="ionic-input" placeholder="0">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">商品分类</label>
                    <select class="ionic-input">
                        <option>选择分类</option>
                        <option>电子产品</option>
                        <option>服装鞋帽</option>
                        <option>食品饮料</option>
                    </select>
                </div>
            </div>
            <div class="flex space-x-3 mt-6">
                <button onclick="closeModal('addProductModal')" class="flex-1 ionic-button bg-gray-100 text-gray-600">取消</button>
                <button onclick="addProduct()" class="flex-1 ionic-button bg-primary text-white">添加商品</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function showAddProductModal() {
            document.getElementById('addProductModal').classList.remove('hidden');
            document.getElementById('addProductModal').classList.add('flex');
        }

        function showScanModal() {
            alert('打开扫码功能');
        }

        function showFilterModal() {
            alert('打开筛选功能');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).classList.remove('flex');
        }

        function addProduct() {
            alert('商品添加成功！');
            closeModal('addProductModal');
        }

        function showProductMenu(productId) {
            alert('显示商品 ' + productId + ' 的操作菜单');
        }

        function quickEdit(productId) {
            alert('快速编辑商品 ' + productId);
        }

        function toggleProductStatus(productId) {
            alert('切换商品 ' + productId + ' 的上架状态');
        }

        function restockProduct(productId) {
            alert('为商品 ' + productId + ' 补货');
        }

        function filterProducts(category) {
            document.querySelectorAll('.filter-active').forEach(btn => {
                btn.classList.remove('filter-active');
                btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-600');
            });
            
            event.target.classList.add('filter-active');
            event.target.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-600');
            
            alert('筛选分类: ' + category);
        }

        function goBack() {
            window.history.back();
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-backdrop')) {
                e.target.classList.add('hidden');
                e.target.classList.remove('flex');
            }
        });
    </script>
</body>

</html>