<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>商家ERP系统 - 移动端</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .mobile-bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 100;
                background-color: white;
                border-top: 1px solid #E2E8F0;
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            }
            .tab-active {
                color: #4F46E5;
                font-weight: 600;
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
            .module-card {
                transition: all 0.3s ease;
            }
            .module-card:active {
                transform: scale(0.98);
                opacity: 0.9;
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slideIn {
            animation: slideIn 0.5s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* 数据卡片动画延迟 */
        .card-delay-1 {
            animation-delay: 0.1s;
        }

        .card-delay-2 {
            animation-delay: 0.2s;
        }

        .card-delay-3 {
            animation-delay: 0.3s;
        }

        .card-delay-4 {
            animation-delay: 0.4s;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        /* 页面切换动画 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        /* 登录页面特定样式 */
        .login-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
        }

        .login-form-container {
            background: white;
            border-radius: 24px 24px 0 0;
            padding: 32px;
            flex: 1;
            transform: translateY(15%);
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 - 仪表盘 -->
    <header id="dashboardHeader"
        class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-bold">商家ERP系统</h1>
            <div class="flex items-center space-x-4">
                <button class="relative p-2">
                    <i class="fa fa-bell text-lg"></i>
                    <span
                        class="absolute top-1 right-1 bg-danger text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
                <img src="https://picsum.photos/id/64/200/200" alt="用户头像"
                    class="h-8 w-8 rounded-full object-cover border-2 border-white">
            </div>
        </div>
    </header>

    <!-- 顶部导航栏 - 其他页面 -->
    <header id="otherPageHeader"
        class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md hidden">
        <div class="flex items-center">
            <button id="backButton" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2" id="pageTitle">页面标题</h1>
        </div>
    </header>

    <!-- 登录页面 -->
    <div id="loginPage" class="page active login-container">
        <div class="flex-1 flex flex-col items-center justify-center px-6 text-white">
            <div class="text-center mb-8">
                <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa fa-cubes text-primary text-4xl"></i>
                </div>
                <h1 class="text-3xl font-bold mb-2">商家ERP系统</h1>
                <p class="text-white/80">高效管理您的店铺业务</p>
            </div>
        </div>

        <div class="login-form-container">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-center mb-6">欢迎回来</h2>

                <div class="space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                <i class="fa fa-user"></i>
                            </span>
                            <input type="text" id="username" class="ionic-input pl-10" placeholder="请输入用户名">
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                <i class="fa fa-lock"></i>
                            </span>
                            <input type="password" id="password" class="ionic-input pl-10" placeholder="请输入密码">
                            <button class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400">
                                <i class="fa fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button class="text-primary text-sm font-medium">忘记密码？</button>
                    </div>
                </div>
            </div>

            <button id="loginButton" class="w-full ionic-button bg-primary text-white py-6">
                登录
            </button>

            <div class="text-center mt-6">
                <p class="text-gray-500 text-sm">
                    还没有账号？ <span class="text-primary font-medium">立即注册</span>
                </p>
            </div>
        </div>
    </div>

    <!-- 模块索引页 -->
    <div id="modulesPage" class="page">
        <!-- 顶部导航栏 - 模块索引页 -->
        <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-bold">功能模块</h1>
                <button id="logoutButton" class="p-2">
                    <i class="fa fa-sign-out text-lg"></i>
                </button>
            </div>
        </header>

        <main class="container mx-auto px-4 pt-28 pb-8">
            <!-- 用户信息 -->
            <div class="text-center mb-8">
                <img src="https://picsum.photos/id/64/200/200" alt="用户头像"
                    class="h-20 w-20 rounded-full object-cover border-4 border-primary mx-auto mb-3">
                <h2 class="text-xl font-bold">您好，王老板！</h2>
                <p class="text-gray-500">高级管理员</p>
            </div>

            <!-- 快捷操作 -->
            <section class="grid grid-cols-4 gap-4 mb-8">
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                        <i class="fa fa-plus text-primary text-xl"></i>
                    </div>
                    <span class="text-xs text-center">新建订单</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-secondary/10 flex items-center justify-center mb-2">
                        <i class="fa fa-cube text-secondary text-xl"></i>
                    </div>
                    <span class="text-xs text-center">新增商品</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-accent/10 flex items-center justify-center mb-2">
                        <i class="fa fa-barcode text-accent text-xl"></i>
                    </div>
                    <span class="text-xs text-center">扫码入库</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-danger/10 flex items-center justify-center mb-2">
                        <i class="fa fa-qrcode text-danger text-xl"></i>
                    </div>
                    <span class="text-xs text-center">扫码出库</span>
                </div>
            </section>

            <!-- 功能模块导航 -->
            <section class="mb-6">
                <h3 class="text-base font-semibold mb-4">核心功能</h3>
                <div class="grid grid-cols-2 gap-4">
                    <!-- 仪表盘模块 -->
                    <div class="ionic-card module-card hover-lift" onclick="showPage('dashboardPage', '仪表盘')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                    <i class="fa fa-dashboard text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">数据仪表盘</h4>
                                    <p class="text-gray-500 text-xs">业务数据概览</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 商品管理模块 -->
                    <div class="ionic-card module-card hover-lift" onclick="showPage('productsPage', '商品管理')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                    <i class="fa fa-cube text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">商品管理</h4>
                                    <p class="text-gray-500 text-xs">管理商品信息和库存</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 订单管理模块 -->
                    <div class="ionic-card module-card hover-lift" onclick="showPage('ordersPage', '订单管理')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                                    <i class="fa fa-shopping-cart text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">订单管理</h4>
                                    <p class="text-gray-500 text-xs">查看和处理订单</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 报表分析模块 -->
                    <div class="ionic-card module-card hover-lift" onclick="showPage('reportsPage', '报表分析')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                                    <i class="fa fa-bar-chart text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">报表分析</h4>
                                    <p class="text-gray-500 text-xs">销售和库存报表</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 个人中心模块 -->
                    <div class="ionic-card module-card hover-lift" onclick="showPage('profilePage', '个人中心')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-danger/10 flex items-center justify-center text-danger mr-3">
                                    <i class="fa fa-user text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">个人中心</h4>
                                    <p class="text-gray-500 text-xs">设置和个人信息</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 权限管理模块 -->
                    <div class="ionic-card module-card hover-lift">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                                    <i class="fa fa-lock text-lg"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">权限管理</h4>
                                    <p class="text-gray-500 text-xs">用户角色和权限</p>
                                </div>
                            </div>
                            <i class="fa fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-8">
        <!-- 仪表盘页面 -->
        <div id="dashboardPage" class="page">
            <!-- 欢迎信息和日期 -->
            <section class="mb-6">
                <div>
                    <h2 class="text-xl font-bold">您好，王老板！</h2>
                    <p class="text-gray-500 mt-1" id="currentDate">2023年10月15日 星期日</p>
                </div>
            </section>

            <!-- 快速操作按钮 -->
            <section class="grid grid-cols-4 gap-4 mb-6">
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                        <i class="fa fa-plus text-primary text-xl"></i>
                    </div>
                    <span class="text-xs text-center">新建订单</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-secondary/10 flex items-center justify-center mb-2">
                        <i class="fa fa-cube text-secondary text-xl"></i>
                    </div>
                    <span class="text-xs text-center">新增商品</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-accent/10 flex items-center justify-center mb-2">
                        <i class="fa fa-barcode text-accent text-xl"></i>
                    </div>
                    <span class="text-xs text-center">扫码入库</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-danger/10 flex items-center justify-center mb-2">
                        <i class="fa fa-qrcode text-danger text-xl"></i>
                    </div>
                    <span class="text-xs text-center">扫码出库</span>
                </div>
            </section>

            <!-- 数据概览卡片 -->
            <section class="space-y-4 mb-6">
                <!-- 总销售额卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn card-delay-1">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">总销售额</p>
                            <h3 class="text-2xl font-bold mt-1">¥128,560</h3>
                        </div>
                        <div class="p-3 bg-primary/10 text-primary rounded-lg">
                            <i class="fa fa-line-chart text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-secondary mt-2">
                        <i class="fa fa-arrow-up"></i>
                        <span class="font-medium">12.5%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>

                <!-- 订单数量卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn card-delay-2">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">订单数量</p>
                            <h3 class="text-2xl font-bold mt-1">2,845</h3>
                        </div>
                        <div class="p-3 bg-secondary/10 text-secondary rounded-lg">
                            <i class="fa fa-shopping-cart text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-secondary mt-2">
                        <i class="fa fa-arrow-up"></i>
                        <span class="font-medium">8.3%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>

                <!-- 商品库存卡片 -->
                <div class="ionic-card hover-lift animate-fadeIn card-delay-3">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-gray-500 font-medium text-sm">商品库存</p>
                            <h3 class="text-2xl font-bold mt-1">3,456</h3>
                        </div>
                        <div class="p-3 bg-accent/10 text-accent rounded-lg">
                            <i class="fa fa-cubes text-xl"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1 text-danger mt-2">
                        <i class="fa fa-arrow-down"></i>
                        <span class="font-medium">5.2%</span>
                        <span class="text-gray-500 text-xs">较上月</span>
                    </div>
                </div>
            </section>

            <!-- 销售趋势图表 -->
            <section class="ionic-card mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-semibold">销售趋势</h3>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 text-xs bg-primary text-white rounded-full">周</button>
                        <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">月</button>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="mobileSalesChart"></canvas>
                </div>
            </section>

            <!-- 最近订单 -->
            <section class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-semibold">最近订单</h3>
                    <button class="text-primary text-xs font-medium"
                        onclick="showPage('ordersPage', '订单管理')">查看全部</button>
                </div>
                <div class="space-y-3">
                    <div class="ionic-card">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h4 class="font-medium">ORD-2023-1015-001</h4>
                                <p class="text-gray-500 text-xs">客户：张三</p>
                            </div>
                            <span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <p class="text-gray-500 text-xs">2023-10-15 10:30</p>
                            <p class="font-medium">¥2,580.00</p>
                        </div>
                    </div>
                    <div class="ionic-card">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h4 class="font-medium">ORD-2023-1015-002</h4>
                                <p class="text-gray-500 text-xs">客户：李四</p>
                            </div>
                            <span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-xs">处理中</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <p class="text-gray-500 text-xs">2023-10-15 09:15</p>
                            <p class="font-medium">¥1,399.00</p>
                        </div>
                    </div>
                    <div class="ionic-card">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h4 class="font-medium">ORD-2023-1014-567</h4>
                                <p class="text-gray-500 text-xs">客户：王五</p>
                            </div>
                            <span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <p class="text-gray-500 text-xs">2023-10-14 16:45</p>
                            <p class="font-medium">¥8,999.00</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 低库存商品 -->
            <section>
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-semibold">低库存商品</h3>
                    <button class="text-primary text-xs font-medium"
                        onclick="showPage('productsPage', '商品管理')">查看全部</button>
                </div>
                <div class="space-y-3">
                    <!-- 商品1 -->
                    <div class="ionic-card">
                        <div class="flex items-center">
                            <img src="https://picsum.photos/id/26/200/200" alt="智能手表"
                                class="h-16 w-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium">智能手表 Pro</h4>
                                <p class="text-gray-500 text-xs">电子产品</p>
                                <div class="flex items-center space-x-1 text-danger mt-1">
                                    <i class="fa fa-exclamation-circle text-xs"></i>
                                    <span class="text-sm font-medium">仅余12件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 商品2 -->
                    <div class="ionic-card">
                        <div class="flex items-center">
                            <img src="https://picsum.photos/id/96/200/200" alt="无线耳机"
                                class="h-16 w-16 rounded-lg object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium">无线蓝牙耳机</h4>
                                <p class="text-gray-500 text-xs">电子产品</p>
                                <div class="flex items-center space-x-1 text-danger mt-1">
                                    <i class="fa fa-exclamation-circle text-xs"></i>
                                    <span class="text-sm font-medium">仅余8件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- 商品管理页面 -->
        <div id="productsPage" class="page">
            <div class="flex items-center mb-4">
                <div class="relative flex-1">
                    <input type="text" placeholder="搜索商品..." class="ionic-input pl-10">
                    <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button class="ml-3 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">
                    <i class="fa fa-plus"></i>
                </button>
            </div>

            <div class="space-y-3">
                <!-- 商品1 -->
                <div class="ionic-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/26/200/200" alt="智能手表"
                            class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">智能手表 Pro</h4>
                            <p class="text-gray-500 text-xs">电子产品</p>
                            <div class="flex justify-between items-center mt-1">
                                <div class="flex items-center">
                                    <span class="text-sm font-medium">¥1,299</span>
                                    <span class="text-gray-500 text-xs ml-2">库存: 12</span>
                                </div>
                                <button class="text-primary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品2 -->
                <div class="ionic-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/96/200/200" alt="无线耳机"
                            class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">无线蓝牙耳机</h4>
                            <p class="text-gray-500 text-xs">电子产品</p>
                            <div class="flex justify-between items-center mt-1">
                                <div class="flex items-center">
                                    <span class="text-sm font-medium">¥899</span>
                                    <span class="text-gray-500 text-xs ml-2">库存: 8</span>
                                </div>
                                <button class="text-primary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品3 -->
                <div class="ionic-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/3/200/200" alt="休闲运动鞋"
                            class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">轻便休闲运动鞋</h4>
                            <p class="text-gray-500 text-xs">服装鞋帽</p>
                            <div class="flex justify-between items-center mt-1">
                                <div class="flex items-center">
                                    <span class="text-sm font-medium">¥399</span>
                                    <span class="text-gray-500 text-xs ml-2">库存: 15</span>
                                </div>
                                <button class="text-primary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品4 -->
                <div class="ionic-card">
                    <div class="flex items-center">
                        <img src="https://picsum.photos/id/431/200/200" alt="高端巧克力"
                            class="h-16 w-16 rounded-lg object-cover">
                        <div class="ml-3 flex-1">
                            <h4 class="font-medium">礼盒装巧克力</h4>
                            <p class="text-gray-500 text-xs">食品饮料</p>
                            <div class="flex justify-between items-center mt-1">
                                <div class="flex items-center">
                                    <span class="text-sm font-medium">¥199</span>
                                    <span class="text-gray-500 text-xs ml-2">库存: 5</span>
                                </div>
                                <button class="text-primary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单管理页面 -->
        <div id="ordersPage" class="page">
            <div class="flex items-center mb-4">
                <div class="relative flex-1">
                    <input type="text" placeholder="搜索订单..." class="ionic-input pl-10">
                    <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <button class="ml-3 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">
                    <i class="fa fa-plus"></i>
                </button>
            </div>

            <!-- 订单状态筛选 -->
            <div class="flex overflow-x-auto space-x-2 mb-4 pb-2 scrollbar-hide">
                <button class="px-3 py-1.5 bg-primary text-white rounded-full text-sm whitespace-nowrap">全部</button>
                <button
                    class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">待付款</button>
                <button
                    class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">处理中</button>
                <button
                    class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">已完成</button>
                <button
                    class="px-3 py-1.5 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">已取消</button>
            </div>

            <div class="space-y-3">
                <!-- 订单1 -->
                <div class="ionic-card">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="font-medium">ORD-2023-1015-001</h4>
                            <p class="text-gray-500 text-xs">客户：张三</p>
                        </div>
                        <span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="text-gray-500 text-xs">2023-10-15 10:30</p>
                        <p class="font-medium">¥2,580.00</p>
                    </div>
                </div>

                <!-- 订单2 -->
                <div class="ionic-card">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="font-medium">ORD-2023-1015-002</h4>
                            <p class="text-gray-500 text-xs">客户：李四</p>
                        </div>
                        <span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-xs">处理中</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="text-gray-500 text-xs">2023-10-15 09:15</p>
                        <p class="font-medium">¥1,399.00</p>
                    </div>
                </div>

                <!-- 订单3 -->
                <div class="ionic-card">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="font-medium">ORD-2023-1014-567</h4>
                            <p class="text-gray-500 text-xs">客户：王五</p>
                        </div>
                        <span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-xs">已完成</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="text-gray-500 text-xs">2023-10-14 16:45</p>
                        <p class="font-medium">¥8,999.00</p>
                    </div>
                </div>

                <!-- 订单4 -->
                <div class="ionic-card">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="font-medium">ORD-2023-1014-566</h4>
                            <p class="text-gray-500 text-xs">客户：赵六</p>
                        </div>
                        <span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-xs">已取消</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="text-gray-500 text-xs">2023-10-14 14:20</p>
                        <p class="font-medium">¥1,250.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表分析页面 -->
        <div id="reportsPage" class="page">
            <!-- 报表类型选择 -->
            <div class="flex overflow-x-auto space-x-2 mb-6 pb-2 scrollbar-hide">
                <button class="px-4 py-2 bg-primary text-white rounded-full text-sm whitespace-nowrap">销售报表</button>
                <button
                    class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">采购报表</button>
                <button
                    class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">库存报表</button>
                <button
                    class="px-4 py-2 bg-white border border-gray-200 rounded-full text-sm whitespace-nowrap">客户报表</button>
            </div>

            <!-- 时间筛选 -->
            <div class="ionic-card mb-6">
                <div class="flex justify-between items-center">
                    <button class="px-3 py-1.5 bg-primary/10 text-primary rounded-lg text-sm">本周</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本月</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本季度</button>
                    <button class="px-3 py-1.5 bg-white border border-gray-200 rounded-lg text-sm">本年</button>
                </div>
            </div>

            <!-- 销售趋势图表 -->
            <section class="ionic-card mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-semibold">销售趋势</h3>
                </div>
                <div class="h-64">
                    <canvas id="reportsSalesChart"></canvas>
                </div>
            </section>

            <!-- 销售分类统计 -->
            <section class="ionic-card mb-6">
                <h3 class="text-base font-semibold mb-4">销售分类</h3>
                <div class="h-64 mb-4">
                    <canvas id="reportsCategoryChart"></canvas>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-primary"></span>
                            <span class="text-sm text-gray-700">电子产品</span>
                        </div>
                        <span class="font-medium">45%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-secondary"></span>
                            <span class="text-sm text-gray-700">服装鞋帽</span>
                        </div>
                        <span class="font-medium">25%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="h-3 w-3 rounded-full bg-accent"></span>
                            <span class="text-sm text-gray-700">食品饮料</span>
                        </div>
                        <span class="font-medium">15%</span>
                    </div>
                </div>
            </section>

            <!-- 导出按钮 -->
            <button class="w-full ionic-button bg-primary text-white">
                <i class="fa fa-download mr-2"></i>
                导出报表
            </button>
        </div>

        <!-- 个人中心页面 -->
        <div id="profilePage" class="page">
            <!-- 用户信息 -->
            <div class="flex flex-col items-center p-6 bg-white rounded-xl mb-6">
                <img src="https://picsum.photos/id/64/200/200" alt="用户头像"
                    class="h-20 w-20 rounded-full object-cover border-4 border-primary">
                <h3 class="text-xl font-bold mt-4">王老板</h3>
                <p class="text-gray-500 mt-1">高级管理员</p>
            </div>

            <!-- 功能菜单 -->
            <div class="space-y-1 mb-6">
                <div class="ionic-card p-4">
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                            <i class="fa fa-user"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">个人资料</h4>
                            <p class="text-gray-500 text-xs">查看和编辑个人信息</p>
                        </div>
                        <i class="fa fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="ionic-card p-4">
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-3">
                            <i class="fa fa-cog"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">系统设置</h4>
                            <p class="text-gray-500 text-xs">应用配置和偏好设置</p>
                        </div>
                        <i class="fa fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="ionic-card p-4">
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 rounded-full bg-accent/10 flex items-center justify-center text-accent mr-3">
                            <i class="fa fa-lock"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">权限管理</h4>
                            <p class="text-gray-500 text-xs">管理用户角色和权限</p>
                        </div>
                        <i class="fa fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="ionic-card p-4">
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
                            <i class="fa fa-question-circle"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">帮助与反馈</h4>
                            <p class="text-gray-500 text-xs">获取帮助和提交反馈</p>
                        </div>
                        <i class="fa fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- 退出登录 -->
            <button class="w-full ionic-button bg-white border border-danger text-danger">
                <i class="fa fa-sign-out mr-2"></i>
                退出登录
            </button>
        </div>
    </main>

    <!-- 脚本 -->
    <script>
        // 设置当前日期
        document.addEventListener('DOMContentLoaded', function () {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('zh-CN', options);

            // 登录按钮点击事件
            document.getElementById('loginButton').addEventListener('click', function () {
                // 简单的登录验证示例
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (username && password) {
                    // 登录成功，跳转到模块索引页
                    showPage('modulesPage');
                } else {
                    // 提示用户输入用户名和密码
                    alert('请输入用户名和密码');
                }
            });

            // 退出登录按钮点击事件
            document.getElementById('logoutButton').addEventListener('click', function () {
                showPage('loginPage');
            });

            // 初始化移动端销售趋势图表
            const mobileSalesCtx = document.getElementById('mobileSalesChart').getContext('2d');
            const mobileSalesChart = new Chart(mobileSalesCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '销售额',
                        data: [12500, 15600, 13200, 18900, 16500, 21400, 20300],
                        borderColor: '#4F46E5',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#4F46E5',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            displayColors: false,
                            callbacks: {
                                label: function (context) {
                                    return '销售额: ¥' + context.raw.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            grid: {
                                color: '#E2E8F0'
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                },
                                callback: function (value) {
                                    return '¥' + (value / 1000) + 'k';
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    animations: {
                        tension: {
                            duration: 1000,
                            easing: 'linear'
                        }
                    }
                }
            });

            // 初始化报表页面销售图表
            const reportsSalesCtx = document.getElementById('reportsSalesChart').getContext('2d');
            const reportsSalesChart = new Chart(reportsSalesCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额',
                        data: [125000, 156000, 132000, 189000, 165000, 214000],
                        backgroundColor: '#4F46E5',
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            displayColors: false,
                            callbacks: {
                                label: function (context) {
                                    return '销售额: ¥' + context.raw.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            grid: {
                                color: '#E2E8F0'
                            },
                            ticks: {
                                color: '#94A3B8',
                                font: {
                                    size: 10
                                },
                                callback: function (value) {
                                    return '¥' + (value / 1000) + 'k';
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });

            // 初始化销售分类图表
            const reportsCategoryCtx = document.getElementById('reportsCategoryChart').getContext('2d');
            const reportsCategoryChart = new Chart(reportsCategoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['电子产品', '服装鞋帽', '食品饮料', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 15],
                        backgroundColor: [
                            '#4F46E5',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(30, 41, 59, 0.8)',
                            padding: 10,
                            cornerRadius: 6,
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            callbacks: {
                                label: function (context) {
                                    return context.label + ': ' + context.raw + '%';
                                }
                            }
                        }
                    },
                    animations: {
                        animateRotate: true,
                        animateScale: true
                    }
                }
            });
        });

        // 页面切换函数
        function showPage(pageId, pageTitle) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('active');
            });

            // 显示选中页面
            const activePage = document.getElementById(pageId);
            activePage.classList.add('active');

            // 更新顶部导航栏
            const dashboardHeader = document.getElementById('dashboardHeader');
            const otherPageHeader = document.getElementById('otherPageHeader');

            // 隐藏所有固定的导航栏
            const allHeaders = document.querySelectorAll('header');
            allHeaders.forEach(header => {
                header.style.display = 'none';
            });

            if (pageId === 'loginPage') {
                // 登录页面不需要导航栏
                return;
            } else if (pageId === 'modulesPage') {
                // 模块索引页有自己的导航栏
                return;
            } else if (pageId === 'dashboardPage') {
                // 显示仪表盘的顶部导航
                dashboardHeader.style.display = 'block';
            } else {
                // 显示其他页面的顶部导航（带返回按钮）
                otherPageHeader.style.display = 'block';
                document.getElementById('pageTitle').textContent = pageTitle || '页面标题';
            }

            // 滚动到页面顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 返回按钮事件监听
        document.getElementById('backButton').addEventListener('click', function () {
            // 从功能页面返回到模块索引页
            showPage('modulesPage');
        });
    </script>
</body>

</html>