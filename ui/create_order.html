<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>新建订单 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .bottom-action-bar {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 100;
                background-color: white;
                border-top: 1px solid #E2E8F0;
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .ionic-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='m19.5 8.25-7.5 7.5-7.5-7.5' /%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px 20px;
        }

        .ionic-select:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .quantity-input {
            display: flex;
            align-items: center;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            overflow: hidden;
        }

        .quantity-input button {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F1F5F9;
            color: #64748B;
            border: none;
            transition: all 0.2s ease;
        }

        .quantity-input button:hover {
            background-color: #E2E8F0;
        }

        .quantity-input input {
            width: 60px;
            height: 40px;
            text-align: center;
            border: none;
            outline: none;
            font-weight: 500;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button id="backButton" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">新建订单</h1>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-32">
        <!-- 订单表单 -->
        <div class="space-y-6">
            <!-- 订单基本信息 -->
            <section class="animate-fadeIn">
                <h2 class="text-lg font-semibold mb-4">订单基本信息</h2>
                <div class="ionic-card">
                    <div class="space-y-4">
                        <div>
                            <label for="customerName" class="block text-sm font-medium text-gray-700 mb-1">客户名称</label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                    <i class="fa fa-user"></i>
                                </span>
                                <input type="text" id="customerName" class="ionic-input pl-10" placeholder="请输入客户名称">
                            </div>
                        </div>

                        <div>
                            <label for="customerPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                    <i class="fa fa-phone"></i>
                                </span>
                                <input type="tel" id="customerPhone" class="ionic-input pl-10" placeholder="请输入联系电话">
                            </div>
                        </div>

                        <div>
                            <label for="orderDate" class="block text-sm font-medium text-gray-700 mb-1">订单日期</label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                    <i class="fa fa-calendar"></i>
                                </span>
                                <input type="date" id="orderDate" class="ionic-input pl-10">
                            </div>
                        </div>

                        <div>
                            <label for="orderType" class="block text-sm font-medium text-gray-700 mb-1">订单类型</label>
                            <div class="relative">
                                <select id="orderType" class="ionic-select">
                                    <option value="retail">零售订单</option>
                                    <option value="wholesale">批发订单</option>
                                    <option value="online">线上订单</option>
                                    <option value="custom">定制订单</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="orderRemark" class="block text-sm font-medium text-gray-700 mb-1">订单备注</label>
                            <textarea id="orderRemark" class="ionic-input" rows="3" placeholder="请输入订单备注信息"></textarea>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 添加商品区域 -->
            <section class="animate-fadeIn" style="animation-delay: 0.1s;">
                <h2 class="text-lg font-semibold mb-4">添加商品</h2>
                <div class="ionic-card">
                    <div class="flex items-center mb-4">
                        <div class="relative flex-1">
                            <input type="text" id="productSearch" class="ionic-input pl-10" placeholder="搜索商品...">
                            <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <button id="scanBarcode" class="ml-3 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">
                            <i class="fa fa-barcode"></i>
                        </button>
                    </div>

                    <!-- 商品搜索结果（模拟） -->
                    <div id="productSearchResults" class="space-y-2 max-h-40 overflow-y-auto border rounded-lg p-2 bg-gray-50">
                        <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer" onclick="addProduct('智能手表 Pro', 1299, 'https://picsum.photos/id/26/200/200')">
                            <img src="https://picsum.photos/id/26/200/200" alt="智能手表" class="h-12 w-12 rounded object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-sm">智能手表 Pro</h4>
                                <p class="text-gray-500 text-xs">电子产品 | 库存: 12</p>
                            </div>
                            <span class="font-medium">¥1,299</span>
                        </div>
                        <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer" onclick="addProduct('无线蓝牙耳机', 899, 'https://picsum.photos/id/96/200/200')">
                            <img src="https://picsum.photos/id/96/200/200" alt="无线耳机" class="h-12 w-12 rounded object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-sm">无线蓝牙耳机</h4>
                                <p class="text-gray-500 text-xs">电子产品 | 库存: 8</p>
                            </div>
                            <span class="font-medium">¥899</span>
                        </div>
                        <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer" onclick="addProduct('轻便休闲运动鞋', 399, 'https://picsum.photos/id/3/200/200')">
                            <img src="https://picsum.photos/id/3/200/200" alt="休闲运动鞋" class="h-12 w-12 rounded object-cover">
                            <div class="ml-3 flex-1">
                                <h4 class="font-medium text-sm">轻便休闲运动鞋</h4>
                                <p class="text-gray-500 text-xs">服装鞋帽 | 库存: 15</p>
                            </div>
                            <span class="font-medium">¥399</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 订单商品列表 -->
            <section class="animate-fadeIn" style="animation-delay: 0.2s;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">订单商品</h2>
                    <span id="orderItemCount" class="text-sm text-gray-500">共 0 件商品</span>
                </div>
                <div id="orderItemsList" class="ionic-card space-y-4">
                    <!-- 初始状态提示 -->
                    <div id="emptyOrderList" class="text-center py-8 text-gray-500">
                        <i class="fa fa-shopping-cart text-4xl mb-3 text-gray-300"></i>
                        <p>暂无商品，请添加商品到订单</p>
                    </div>
                    <!-- 商品列表会通过JS动态添加 -->
                </div>
            </section>

            <!-- 订单总计 -->
            <section class="animate-fadeIn" style="animation-delay: 0.3s;">
                <h2 class="text-lg font-semibold mb-4">订单总计</h2>
                <div class="ionic-card">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">商品总额</span>
                            <span id="subtotal">¥0.00</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">运费</span>
                            <div class="flex items-center">
                                <input type="number" id="shippingFee" value="0" min="0" step="0.01" class="w-20 text-right border-b border-gray-300 px-2 py-1 focus:outline-none focus:border-primary" onchange="calculateTotal()">
                            </div>
                        </div>
                        <div class="border-t border-gray-200 pt-3 flex justify-between items-center">
                            <span class="font-semibold">订单总价</span>
                            <span id="totalPrice" class="text-lg font-bold text-primary">¥0.00</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 支付方式 -->
            <section class="animate-fadeIn" style="animation-delay: 0.4s;">
                <h2 class="text-lg font-semibold mb-4">支付方式</h2>
                <div class="ionic-card">
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <input type="radio" id="paymentCash" name="paymentMethod" value="cash" class="w-5 h-5 text-primary focus:ring-primary" checked>
                            <label for="paymentCash" class="flex-1 cursor-pointer">现金支付</label>
                            <i class="fa fa-money text-xl text-primary"></i>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="radio" id="paymentAlipay" name="paymentMethod" value="alipay" class="w-5 h-5 text-primary focus:ring-primary">
                            <label for="paymentAlipay" class="flex-1 cursor-pointer">支付宝</label>
                            <i class="fa fa-credit-card-alt text-xl text-blue-500"></i>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="radio" id="paymentWechat" name="paymentMethod" value="wechat" class="w-5 h-5 text-primary focus:ring-primary">
                            <label for="paymentWechat" class="flex-1 cursor-pointer">微信支付</label>
                            <i class="fa fa-weixin text-xl text-green-500"></i>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="radio" id="paymentBank" name="paymentMethod" value="bank" class="w-5 h-5 text-primary focus:ring-primary">
                            <label for="paymentBank" class="flex-1 cursor-pointer">银行转账</label>
                            <i class="fa fa-university text-xl text-purple-500"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar p-4">
        <div class="flex space-x-4">
            <button id="saveDraftButton" class="flex-1 py-3 border border-gray-300 rounded-lg font-medium text-gray-700">
                <i class="fa fa-save mr-2"></i>保存为草稿
            </button>
            <button id="submitOrderButton" class="flex-1 py-3 bg-primary text-white rounded-lg font-medium">
                <i class="fa fa-check mr-2"></i>提交订单
            </button>
        </div>
    </div>

    <!-- 脚本 -->
    <script>
        // 设置当前日期
        document.addEventListener('DOMContentLoaded', function () {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            document.getElementById('orderDate').value = `${year}-${month}-${day}`;
        });

        // 商品列表数据
        let orderItems = [];

        // 添加商品到订单
        function addProduct(name, price, image) {
            // 检查商品是否已在订单中
            const existingItemIndex = orderItems.findIndex(item => item.name === name);
            
            if (existingItemIndex >= 0) {
                // 商品已存在，增加数量
                orderItems[existingItemIndex].quantity += 1;
            } else {
                // 添加新商品
                orderItems.push({
                    id: Date.now(), // 使用时间戳作为临时ID
                    name: name,
                    price: price,
                    quantity: 1,
                    image: image
                });
            }
            
            // 更新订单列表显示
            updateOrderItemsList();
            // 计算总价
            calculateTotal();
        }

        // 更新订单商品列表显示
        function updateOrderItemsList() {
            const orderItemsList = document.getElementById('orderItemsList');
            const emptyOrderList = document.getElementById('emptyOrderList');
            const orderItemCount = document.getElementById('orderItemCount');
            
            // 更新商品数量
            orderItemCount.textContent = `共 ${orderItems.length} 件商品`;
            
            // 隐藏空状态提示
            if (orderItems.length > 0) {
                emptyOrderList.classList.add('hidden');
            } else {
                emptyOrderList.classList.remove('hidden');
                return;
            }
            
            // 清空列表
            while (orderItemsList.children.length > 1) { // 保留emptyOrderList元素
                orderItemsList.removeChild(orderItemsList.lastChild);
            }
            
            // 添加商品项
            orderItems.forEach(item => {
                const itemTotal = item.price * item.quantity;
                
                const itemElement = document.createElement('div');
                itemElement.className = 'flex items-center border-b border-gray-100 pb-3 last:border-b-0';
                itemElement.innerHTML = `
                    <img src="${item.image}" alt="${item.name}" class="h-16 w-16 rounded-lg object-cover">
                    <div class="ml-3 flex-1">
                        <h4 class="font-medium">${item.name}</h4>
                        <p class="text-gray-500 text-sm mt-1">¥${item.price.toFixed(2)}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="quantity-input">
                            <button onclick="changeQuantity(${item.id}, -1)" class="decrease-btn"><i class="fa fa-minus"></i></button>
                            <input type="number" value="${item.quantity}" min="1" class="quantity-input-field" onchange="updateQuantity(${item.id}, this.value)">
                            <button onclick="changeQuantity(${item.id}, 1)" class="increase-btn"><i class="fa fa-plus"></i></button>
                        </div>
                        <span class="font-medium">¥${itemTotal.toFixed(2)}</span>
                        <button onclick="removeItem(${item.id})" class="text-gray-400 hover:text-danger"><i class="fa fa-trash"></i></button>
                    </div>
                `;
                
                orderItemsList.appendChild(itemElement);
            });
        }

        // 改变商品数量
        function changeQuantity(itemId, change) {
            const itemIndex = orderItems.findIndex(item => item.id === itemId);
            if (itemIndex >= 0) {
                const newQuantity = orderItems[itemIndex].quantity + change;
                if (newQuantity > 0) {
                    orderItems[itemIndex].quantity = newQuantity;
                } else {
                    // 数量为0时移除商品
                    orderItems.splice(itemIndex, 1);
                }
                updateOrderItemsList();
                calculateTotal();
            }
        }

        // 更新商品数量
        function updateQuantity(itemId, newQuantity) {
            const itemIndex = orderItems.findIndex(item => item.id === itemId);
            if (itemIndex >= 0) {
                const quantity = parseInt(newQuantity) || 1;
                if (quantity > 0) {
                    orderItems[itemIndex].quantity = quantity;
                } else {
                    orderItems[itemIndex].quantity = 1;
                }
                updateOrderItemsList();
                calculateTotal();
            }
        }

        // 移除商品
        function removeItem(itemId) {
            orderItems = orderItems.filter(item => item.id !== itemId);
            updateOrderItemsList();
            calculateTotal();
        }

        // 计算订单总价
        function calculateTotal() {
            const subtotalElement = document.getElementById('subtotal');
            const totalPriceElement = document.getElementById('totalPrice');
            const shippingFeeElement = document.getElementById('shippingFee');
            
            // 计算商品总额
            const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            // 获取运费
            const shippingFee = parseFloat(shippingFeeElement.value) || 0;
            
            // 计算订单总价
            const totalPrice = subtotal + shippingFee;
            
            // 更新显示
            subtotalElement.textContent = `¥${subtotal.toFixed(2)}`;
            totalPriceElement.textContent = `¥${totalPrice.toFixed(2)}`;
        }

        // 返回按钮事件
        document.getElementById('backButton').addEventListener('click', function() {
            // 在实际应用中，这里可能会跳转到订单列表页面
            alert('返回订单管理页面');
        });

        // 保存为草稿按钮事件
        document.getElementById('saveDraftButton').addEventListener('click', function() {
            if (orderItems.length === 0) {
                alert('请至少添加一件商品到订单');
                return;
            }
            
            // 收集表单数据
            const formData = {
                customerName: document.getElementById('customerName').value,
                customerPhone: document.getElementById('customerPhone').value,
                orderDate: document.getElementById('orderDate').value,
                orderType: document.getElementById('orderType').value,
                orderRemark: document.getElementById('orderRemark').value,
                shippingFee: parseFloat(document.getElementById('shippingFee').value) || 0,
                paymentMethod: document.querySelector('input[name="paymentMethod"]:checked').value,
                items: orderItems
            };
            
            console.log('保存订单草稿:', formData);
            alert('订单已保存为草稿');
        });

        // 提交订单按钮事件
        document.getElementById('submitOrderButton').addEventListener('click', function() {
            if (orderItems.length === 0) {
                alert('请至少添加一件商品到订单');
                return;
            }
            
            if (!document.getElementById('customerName').value) {
                alert('请输入客户名称');
                return;
            }
            
            // 收集表单数据
            const formData = {
                customerName: document.getElementById('customerName').value,
                customerPhone: document.getElementById('customerPhone').value,
                orderDate: document.getElementById('orderDate').value,
                orderType: document.getElementById('orderType').value,
                orderRemark: document.getElementById('orderRemark').value,
                shippingFee: parseFloat(document.getElementById('shippingFee').value) || 0,
                paymentMethod: document.querySelector('input[name="paymentMethod"]:checked').value,
                items: orderItems
            };
            
            console.log('提交订单:', formData);
            alert('订单已提交成功');
            
            // 重置表单（在实际应用中可能会跳转到订单详情页）
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('orderRemark').value = '';
            document.getElementById('shippingFee').value = '0';
            orderItems = [];
            updateOrderItemsList();
            calculateTotal();
        });
    </script>
</body>

</html>