<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>添加商品 - 商家ERP系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5', // 主色调：紫色
                        secondary: '#10B981', // 辅助色：绿色
                        accent: '#F59E0B', // 强调色：橙色
                        danger: '#EF4444', // 危险色：红色
                        dark: '#1E293B', // 深色
                        light: '#F1F5F9', // 浅色
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .hover-lift:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .ios-status-bar {
                height: 44px;
                background-color: #4F46E5;
            }
        }
    </style>

    <!-- 基础样式 -->
    <style>
        body {
            background-color: #F8FAFC;
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-tap-highlight-color: transparent;
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F1F5F9;
        }

        ::-webkit-scrollbar-thumb {
            background: #CBD5E1;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94A3B8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        /* Ionic风格组件 */
        .ionic-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .ionic-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .ionic-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .ionic-input:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .ionic-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2394A3B8'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 20px 20px;
        }

        .ionic-select:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .ionic-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            transition: all 0.2s ease;
            resize: none;
            min-height: 120px;
        }

        .ionic-textarea:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .image-upload-preview {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #F1F5F9;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #CBD5E1;
            transition: all 0.2s ease;
        }

        .image-upload-preview:hover {
            border-color: #4F46E5;
            background-color: rgba(79, 70, 229, 0.05);
        }

        .image-upload-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>

<body class="font-inter text-dark overflow-x-hidden">
    <!-- iOS状态栏占位 -->
    <div class="ios-status-bar fixed top-0 left-0 right-0 z-50"></div>

    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white fixed top-0 left-0 right-0 z-40 pt-11 pb-3 px-4 shadow-md">
        <div class="flex items-center">
            <button id="backButton" class="p-2 -ml-2">
                <i class="fa fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-xl font-bold ml-2">添加商品</h1>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 pt-28 pb-24 animate-fadeIn">
        <form id="addProductForm" class="space-y-6">
            <!-- 商品图片上传 -->
            <div class="ionic-card">
                <h2 class="text-lg font-semibold mb-4">商品图片</h2>
                <div class="grid grid-cols-3 gap-4">
                    <div class="image-upload-preview" id="mainImagePreview">
                        <input type="file" id="mainImageUpload" accept="image/*" class="hidden">
                        <i class="fa fa-plus text-gray-400 text-2xl"></i>
                    </div>
                    <div class="image-upload-preview" id="secondaryImagePreview1">
                        <input type="file" id="secondaryImageUpload1" accept="image/*" class="hidden">
                        <i class="fa fa-plus text-gray-400 text-2xl"></i>
                    </div>
                    <div class="image-upload-preview" id="secondaryImagePreview2">
                        <input type="file" id="secondaryImageUpload2" accept="image/*" class="hidden">
                        <i class="fa fa-plus text-gray-400 text-2xl"></i>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">点击上传商品图片，最多可上传3张</p>
            </div>

            <!-- 商品基本信息 -->
            <div class="ionic-card">
                <h2 class="text-lg font-semibold mb-4">基本信息</h2>
                
                <!-- 商品名称 -->
                <div class="mb-4">
                    <label for="productName" class="block text-sm font-medium text-gray-700 mb-1">商品名称 <span class="text-danger">*</span></label>
                    <input type="text" id="productName" class="ionic-input" placeholder="请输入商品名称" required>
                </div>

                <!-- 商品分类 -->
                <div class="mb-4">
                    <label for="productCategory" class="block text-sm font-medium text-gray-700 mb-1">商品分类 <span class="text-danger">*</span></label>
                    <select id="productCategory" class="ionic-select" required>
                        <option value="">请选择分类</option>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装鞋帽</option>
                        <option value="food">食品饮料</option>
                        <option value="home">家居用品</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <!-- 商品价格和库存 -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="productPrice" class="block text-sm font-medium text-gray-700 mb-1">销售价格 <span class="text-danger">*</span></label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">¥</span>
                            <input type="number" id="productPrice" class="ionic-input pl-8" placeholder="0.00" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div>
                        <label for="productStock" class="block text-sm font-medium text-gray-700 mb-1">库存数量 <span class="text-danger">*</span></label>
                        <input type="number" id="productStock" class="ionic-input" placeholder="0" min="0" step="1" required>
                    </div>
                </div>

                <!-- 商品描述 -->
                <div>
                    <label for="productDescription" class="block text-sm font-medium text-gray-700 mb-1">商品描述</label>
                    <textarea id="productDescription" class="ionic-textarea" placeholder="请输入商品详细描述"></textarea>
                </div>
            </div>

            <!-- 商品其他信息 -->
            <div class="ionic-card">
                <h2 class="text-lg font-semibold mb-4">其他信息</h2>
                
                <!-- 商品编码 -->
                <div class="mb-4">
                    <label for="productCode" class="block text-sm font-medium text-gray-700 mb-1">商品编码</label>
                    <input type="text" id="productCode" class="ionic-input" placeholder="请输入商品编码">
                </div>

                <!-- 商品品牌 -->
                <div class="mb-4">
                    <label for="productBrand" class="block text-sm font-medium text-gray-700 mb-1">商品品牌</label>
                    <input type="text" id="productBrand" class="ionic-input" placeholder="请输入商品品牌">
                </div>

                <!-- 成本价 -->
                <div class="mb-4">
                    <label for="productCost" class="block text-sm font-medium text-gray-700 mb-1">成本价</label>
                    <div class="relative">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">¥</span>
                        <input type="number" id="productCost" class="ionic-input pl-8" placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>

                <!-- 商品状态 -->
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                        <h3 class="font-medium">上架状态</h3>
                        <p class="text-xs text-gray-500 mt-1">启用后商品将在系统中可见并可销售</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="productStatus" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                    </label>
                </div>
            </div>

            <!-- 提交按钮 -->
            <button type="submit" class="w-full ionic-button bg-primary text-white py-6">
                保存商品
            </button>
        </form>
    </main>

    <!-- 脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 返回按钮事件
            document.getElementById('backButton').addEventListener('click', function () {
                // 在实际应用中，这里应该返回到商品管理页面
                alert('返回商品列表页面');
            });

            // 图片上传预览功能
            function setupImageUpload(previewElementId, inputElementId) {
                const preview = document.getElementById(previewElementId);
                const input = document.getElementById(inputElementId);

                preview.addEventListener('click', function() {
                    input.click();
                });

                input.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();

                        reader.onload = function(event) {
                            // 清空预览区域
                            preview.innerHTML = '';
                            // 创建图片元素
                            const img = document.createElement('img');
                            img.src = event.target.result;
                            preview.appendChild(img);
                            
                            // 添加删除按钮
                            const deleteBtn = document.createElement('button');
                            deleteBtn.className = 'absolute bg-danger/80 text-white rounded-full w-6 h-6 flex items-center justify-center';
                            deleteBtn.innerHTML = '<i class="fa fa-times text-xs"></i>';
                            deleteBtn.style.top = '2px';
                            deleteBtn.style.right = '2px';
                            preview.style.position = 'relative';
                            preview.appendChild(deleteBtn);
                            
                            // 删除图片事件
                            deleteBtn.addEventListener('click', function(e) {
                                e.stopPropagation();
                                preview.innerHTML = '<i class="fa fa-plus text-gray-400 text-2xl"></i>';
                                input.value = '';
                            });
                        }

                        reader.readAsDataURL(e.target.files[0]);
                    }
                });
            }

            // 设置所有图片上传区域
            setupImageUpload('mainImagePreview', 'mainImageUpload');
            setupImageUpload('secondaryImagePreview1', 'secondaryImageUpload1');
            setupImageUpload('secondaryImagePreview2', 'secondaryImageUpload2');

            // 表单提交事件
            document.getElementById('addProductForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 在实际应用中，这里应该收集表单数据并提交到服务器
                const formData = {
                    name: document.getElementById('productName').value,
                    category: document.getElementById('productCategory').value,
                    price: document.getElementById('productPrice').value,
                    stock: document.getElementById('productStock').value,
                    description: document.getElementById('productDescription').value,
                    code: document.getElementById('productCode').value,
                    brand: document.getElementById('productBrand').value,
                    cost: document.getElementById('productCost').value,
                    status: document.getElementById('productStatus').checked
                };
                
                console.log('表单数据:', formData);
                alert('商品添加成功！');
            });
        });
    </script>
</body>

</html>